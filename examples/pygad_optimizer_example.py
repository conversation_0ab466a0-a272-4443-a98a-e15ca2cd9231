"""Example demonstrating the PyGAD optimizer integration with Optimagic.

This example shows how to use the PyGAD genetic algorithm optimizer
to solve various optimization problems.
"""

import numpy as np
import optimagic as om


def sphere_function(x):
    """Simple sphere function: f(x) = sum(x^2).
    
    Global minimum at x = [0, 0, ...] with f(x) = 0.
    """
    return np.sum(x**2)


def rosenbrock_function(x):
    """Rosenbrock function: f(x) = sum(100*(x[i+1] - x[i]^2)^2 + (1 - x[i])^2).
    
    Global minimum at x = [1, 1, ...] with f(x) = 0.
    """
    return np.sum(100.0 * (x[1:] - x[:-1]**2)**2 + (1 - x[:-1])**2)


def rastrigin_function(x):
    """Rastrigin function: f(x) = A*n + sum(x[i]^2 - A*cos(2*pi*x[i])).
    
    Highly multimodal function with many local minima.
    Global minimum at x = [0, 0, ...] with f(x) = 0.
    """
    A = 10
    n = len(x)
    return A * n + np.sum(x**2 - A * np.cos(2 * np.pi * x))


def main():
    """Run optimization examples with PyGAD."""
    print("PyGAD Optimizer Examples")
    print("=" * 50)
    
    # Example 1: Simple sphere function
    print("\n1. Sphere Function Optimization")
    print("-" * 30)
    
    start_params = np.array([2.0, 3.0, -1.5])
    bounds = om.Bounds(
        lower=np.array([-5.0, -5.0, -5.0]), 
        upper=np.array([5.0, 5.0, 5.0])
    )
    
    result = om.minimize(
        fun=sphere_function,
        params=start_params,
        algorithm="pygad_ga",
        bounds=bounds,
        algo_options={
            "stopping_maxiter": 100,
            "population_size": 30,
            "seed": 42,
        },
    )
    
    print(f"Success: {result.success}")
    print(f"Optimal value: {result.fun:.6f}")
    print(f"Optimal parameters: {result.params}")
    print(f"Function evaluations: {result.n_fun_evals}")
    print(f"Iterations: {result.n_iterations}")
    
    # Example 2: Rosenbrock function with custom GA parameters
    print("\n2. Rosenbrock Function with Custom Parameters")
    print("-" * 45)
    
    start_params = np.array([0.0, 0.0])
    bounds = om.Bounds(
        lower=np.array([-2.0, -2.0]), 
        upper=np.array([2.0, 2.0])
    )
    
    result = om.minimize(
        fun=rosenbrock_function,
        params=start_params,
        algorithm="pygad_ga",
        bounds=bounds,
        algo_options={
            "stopping_maxiter": 200,
            "population_size": 50,
            "parent_selection_type": "tournament",
            "K_tournament": 5,
            "crossover_type": "uniform",
            "crossover_probability": 0.8,
            "mutation_type": "random",
            "mutation_probability": 0.1,
            "keep_elitism": 2,
            "seed": 123,
        },
    )
    
    print(f"Success: {result.success}")
    print(f"Optimal value: {result.fun:.6f}")
    print(f"Optimal parameters: {result.params}")
    print(f"Function evaluations: {result.n_fun_evals}")
    print(f"Iterations: {result.n_iterations}")
    
    # Example 3: Multimodal Rastrigin function
    print("\n3. Rastrigin Function (Multimodal)")
    print("-" * 35)
    
    start_params = np.array([3.0, -2.5])
    bounds = om.Bounds(
        lower=np.array([-5.12, -5.12]), 
        upper=np.array([5.12, 5.12])
    )
    
    result = om.minimize(
        fun=rastrigin_function,
        params=start_params,
        algorithm="pygad_ga",
        bounds=bounds,
        algo_options={
            "stopping_maxiter": 150,
            "population_size": 40,
            "parent_selection_type": "rank",
            "crossover_type": "two_points",
            "mutation_type": "random",
            "mutation_percent_genes": 15,
            "keep_elitism": 3,
            "seed": 456,
        },
    )
    
    print(f"Success: {result.success}")
    print(f"Optimal value: {result.fun:.6f}")
    print(f"Optimal parameters: {result.params}")
    print(f"Function evaluations: {result.n_fun_evals}")
    print(f"Iterations: {result.n_iterations}")
    
    # Example 4: Maximization problem
    print("\n4. Maximization Example")
    print("-" * 25)
    
    def negative_sphere(x):
        """Negative sphere function for maximization."""
        return -np.sum(x**2)
    
    start_params = np.array([0.5, 0.5])
    bounds = om.Bounds(
        lower=np.array([-3.0, -3.0]), 
        upper=np.array([3.0, 3.0])
    )
    
    result = om.maximize(
        fun=negative_sphere,
        params=start_params,
        algorithm="pygad_ga",
        bounds=bounds,
        algo_options={
            "stopping_maxiter": 80,
            "population_size": 25,
            "parent_selection_type": "sus",
            "mutation_type": "swap",
            "seed": 789,
        },
    )
    
    print(f"Success: {result.success}")
    print(f"Optimal value: {result.fun:.6f}")
    print(f"Optimal parameters: {result.params}")
    print(f"Function evaluations: {result.n_fun_evals}")
    print(f"Iterations: {result.n_iterations}")
    
    # Example 5: Custom Initial Population
    print("\n5. Custom Initial Population Example")
    print("-" * 37)

    start_params = np.array([1.0, 1.0])
    bounds = om.Bounds(
        lower=np.array([-2.0, -2.0]),
        upper=np.array([2.0, 2.0])
    )

    # Create custom initial population with some good starting points
    custom_population = np.array([
        [0.1, 0.1],   # Close to optimum
        [0.5, -0.5],  # Moderate distance
        [1.0, 1.0],   # Further away
        [-0.2, 0.3],  # Another close point
        [1.5, -1.2],  # Edge case
    ])

    result = om.minimize(
        fun=sphere_function,
        params=start_params,
        algorithm="pygad_ga",
        bounds=bounds,
        algo_options={
            "initial_population": custom_population,
            "stopping_maxiter": 50,
            "mutation_type": "random",
            "mutation_by_replacement": True,
            "save_best_solutions": True,
            "seed": 999,
        },
    )

    print(f"Success: {result.success}")
    print(f"Optimal value: {result.fun:.6f}")
    print(f"Optimal parameters: {result.params}")
    print(f"Function evaluations: {result.n_fun_evals}")
    print(f"Iterations: {result.n_iterations}")

    # Example 6: Advanced Parameters and Stop Criteria
    print("\n6. Advanced Parameters with Stop Criteria")
    print("-" * 42)

    start_params = np.array([2.0, 2.0])
    bounds = om.Bounds(
        lower=np.array([-3.0, -3.0]),
        upper=np.array([3.0, 3.0])
    )

    result = om.minimize(
        fun=sphere_function,
        params=start_params,
        algorithm="pygad_ga",
        bounds=bounds,
        algo_options={
            "stopping_maxiter": 200,  # High limit
            "population_size": 30,
            "gene_type": float,
            "mutation_num_genes": 1,  # Mutate exactly 1 gene
            "allow_duplicate_genes": False,
            "fitness_batch_size": 10,  # Batch evaluation
            "stop_criteria": ["reach_0.01", "saturate_15"],  # Stop early if criteria met
            "parallel_processing": 2,  # Use 2 threads
            "seed": 777,
        },
    )

    print(f"Success: {result.success}")
    print(f"Optimal value: {result.fun:.6f}")
    print(f"Optimal parameters: {result.params}")
    print(f"Function evaluations: {result.n_fun_evals}")
    print(f"Iterations: {result.n_iterations}")
    print(f"Note: May have stopped early due to stop criteria")

    print("\n" + "=" * 50)
    print("All examples completed!")


if __name__ == "__main__":
    try:
        main()
    except ImportError as e:
        if "pygad" in str(e):
            print("Error: PyGAD is not installed.")
            print("Please install it with: pip install pygad")
        else:
            raise
    except Exception as e:
        print(f"An error occurred: {e}")
        raise
