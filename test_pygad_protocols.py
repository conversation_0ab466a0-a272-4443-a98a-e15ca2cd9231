#!/usr/bin/env python3
"""
Test script to demonstrate the usage of PyGAD optimizer with user-defined functions
using proper Protocol type annotations.
"""

import numpy as np
from numpy.typing import NDArray
from typing import Any

# Import the protocols from the PyGAD optimizer
# Note: In actual usage, these would be imported from optimagic
from optimagic.optimizers.pygad_new import (
    ParentSelectionFunction,
    CrossoverFunction,
    MutationFunction
)


# Example user-defined parent selection function
def custom_parent_selection_func(
    fitness: NDArray[np.float64],
    num_parents: int,
    ga_instance: Any
) -> tuple[NDArray[np.float64], NDArray[np.int_]]:
    """Custom parent selection that selects the best parents (elitist selection)."""
    # Sort fitness values in descending order
    fitness_sorted_indices = np.argsort(fitness)[::-1]
    
    # Select the best parents
    parent_indices = fitness_sorted_indices[:num_parents]
    parents = ga_instance.population[parent_indices].copy()
    
    return parents, parent_indices


# Example user-defined crossover function
def custom_crossover_func(
    parents: NDArray[np.float64],
    offspring_size: tuple[int, int],
    ga_instance: Any
) -> NDArray[np.float64]:
    """Custom crossover function implementing uniform crossover."""
    offspring = []
    num_offspring, num_genes = offspring_size
    
    for _ in range(num_offspring):
        # Select two random parents
        parent1_idx = np.random.randint(0, parents.shape[0])
        parent2_idx = np.random.randint(0, parents.shape[0])
        
        parent1 = parents[parent1_idx].copy()
        parent2 = parents[parent2_idx].copy()
        
        # Uniform crossover: randomly select genes from each parent
        child = np.where(np.random.random(num_genes) < 0.5, parent1, parent2)
        offspring.append(child)
    
    return np.array(offspring)


# Example user-defined mutation function
def custom_mutation_func(
    offspring: NDArray[np.float64],
    ga_instance: Any
) -> NDArray[np.float64]:
    """Custom mutation function implementing Gaussian mutation."""
    mutated_offspring = offspring.copy()
    
    for i in range(offspring.shape[0]):
        for j in range(offspring.shape[1]):
            # Apply Gaussian mutation with 10% probability
            if np.random.random() < 0.1:
                mutated_offspring[i, j] += np.random.normal(0, 0.1)
    
    return mutated_offspring


def test_protocol_compliance():
    """Test that our custom functions comply with the Protocol definitions."""

    # Test parent selection function compliance
    parent_selection_func: ParentSelectionFunction = custom_parent_selection_func
    print("✓ Custom parent selection function complies with ParentSelectionFunction protocol")

    # Test crossover function compliance
    crossover_func: CrossoverFunction = custom_crossover_func
    print("✓ Custom crossover function complies with CrossoverFunction protocol")

    # Test mutation function compliance
    mutation_func: MutationFunction = custom_mutation_func
    print("✓ Custom mutation function complies with MutationFunction protocol")

    print("\nAll custom functions comply with their respective protocols!")


def example_usage():
    """Example of how to use the PyGAD optimizer with custom functions."""
    print("\nExample usage with optimagic:")
    print("""
import optimagic as om
import numpy as np

# Define your objective function
def sphere(x):
    return np.sum(x**2)

# Use PyGAD with custom functions
result = om.minimize(
    fun=sphere,
    params=np.array([2.0, 3.0]),
    algorithm="pygad",
    bounds=om.Bounds(lower=[-5, -5], upper=[5, 5]),
    algo_options={
        "num_generations": 50,
        "population_size": 20,
        "parent_selection_type": custom_parent_selection_func,
        "crossover_type": custom_crossover_func,
        "mutation_type": custom_mutation_func,
    }
)
""")


if __name__ == "__main__":
    test_protocol_compliance()
    example_usage()
