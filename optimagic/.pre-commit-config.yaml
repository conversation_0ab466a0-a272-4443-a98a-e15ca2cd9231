---
repos:
  - repo: meta
    hooks:
      - id: check-hooks-apply
      - id: check-useless-excludes
        # - id: identity  # Prints all files passed to pre-commits. Debugging.
  - repo: https://github.com/lyz-code/yamlfix
    rev: 1.17.0
    hooks:
      - id: yamlfix
        exclude: tests/optimagic/optimizers/_pounders/fixtures
  - repo: local
    hooks:
      - id: update-environment-files
        name: check environment file updates
        entry: python .tools/update_envs.py
        language: python
        always_run: true
        require_serial: true
  - repo: local
    hooks:
      - id: update-algo-selection-code
        name: update algo selection code
        entry: python .tools/update_algo_selection_hook.py
        language: python
        files: ^(src/optimagic/optimizers/|src/optimagic/algorithms\.py|\.tools/)
        require_serial: true
        additional_dependencies:
          - hatchling
          - ruff
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
        args:
          - --maxkb=1300
        exclude: tests/optimagic/optimizers/_pounders/fixtures/
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-vcs-permalinks
      - id: check-yaml
      - id: check-toml
      - id: debug-statements
      - id: end-of-file-fixer
      - id: fix-byte-order-marker
        types:
          - text
      - id: forbid-submodules
      - id: mixed-line-ending
        args:
          - --fix=lf
        description: Forces to replace line ending by the UNIX 'lf' character.
      - id: name-tests-test
        args:
          - --pytest-test-first
      - id: no-commit-to-branch
        args:
          - --branch
          - main
      - id: trailing-whitespace
        exclude: docs/
      - id: check-ast
  - repo: https://github.com/adrienverge/yamllint.git
    rev: v1.37.1
    hooks:
      - id: yamllint
        exclude: tests/optimagic/optimizers/_pounders/fixtures
  - repo: https://github.com/PyCQA/docformatter
    rev: eb1df34
    hooks:
      - id: docformatter
        args:
          - --in-place
          - --wrap-summaries
          - '88'
          - --wrap-descriptions
          - '88'
          - --blank
        exclude: src/optimagic/optimization/algo_options.py
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.8
    hooks:
      # Run the linter.
      - id: ruff
        types_or:
          - python
          - pyi
          - jupyter
        args:
          - --fix
      # Run the formatter.
      - id: ruff-format
        types_or:
          - python
          - pyi
          - jupyter
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.22
    hooks:
      - id: mdformat
        additional_dependencies:
          - mdformat-gfm
          - mdformat-gfm-alerts
          - mdformat-ruff
        args:
          - --wrap
          - '88'
        files: (README\.md)
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.22
    hooks:
      - id: mdformat
        additional_dependencies:
          - mdformat-myst
          - mdformat-ruff
        args:
          - --wrap
          - '88'
        files: (docs/.)
        exclude: docs/source/how_to/how_to_specify_algorithm_and_algo_options.md
  - repo: https://github.com/kynan/nbstripout
    rev: 0.8.1
    hooks:
      - id: nbstripout
        exclude: |
          (?x)^(
            docs/source/estimagic/tutorials/estimation_tables_overview.ipynb|
            docs/source/estimagic/explanation/bootstrap_montecarlo_comparison.ipynb|
          )$
        args:
          - --drop-empty-cells
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.15.0
    hooks:
      - id: mypy
        files: src|tests
        additional_dependencies:
          - numpy >= 2
          - packaging
          - pandas-stubs
          - sqlalchemy-stubs
          - types-cffi
          - types-openpyxl
          - types-jinja2
        args:
          - --config=pyproject.toml
ci:
  autoupdate_schedule: monthly
  skip:
    - update-algo-selection-code
