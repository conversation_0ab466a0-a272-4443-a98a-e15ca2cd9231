---
name: optimagic
channels:
  - conda-forge
  - nodefaults
dependencies:
  - cyipopt>=1.4.0  # dev, tests
  - pygmo>=2.19.0  # dev, tests, docs
  - nlopt  # dev, tests, docs
  - pip  # dev, tests, docs
  - pytest  # dev, tests
  - pytest-cov  # tests
  - pytest-xdist  # dev, tests
  - statsmodels  # dev, tests
  - cloudpickle  # run, tests
  - joblib  # run, tests
  - numpy >= 2  # run, tests
  - pandas  # run, tests
  - plotly<6.0.0  # run, tests
  - pybaum>=0.1.2  # run, tests
  - scipy>=1.2.1  # run, tests
  - sqlalchemy  # run, tests
  - seaborn  # dev, tests
  - mypy=1.14.1  # dev, tests
  - pyyaml  # dev, tests
  - jinja2  # dev, tests
  - annotated-types  # dev, tests
  - iminuit  # dev, tests
  - pip:  # dev, tests, docs
      - nevergrad  # dev, tests
      - DFO-LS>=1.5.3  # dev, tests
      - Py-BOBYQA  # dev, tests
      - fides==0.7.4  # dev, tests
      - kaleido  # dev, tests
      - pandas-stubs  # dev, tests
      - types-cffi  # dev, tests
      - types-openpyxl  # dev, tests
      - types-jinja2  # dev, tests
      - sqlalchemy-stubs  # dev, tests
      - sphinxcontrib-mermaid  # dev, tests, docs
      - -e ../../
