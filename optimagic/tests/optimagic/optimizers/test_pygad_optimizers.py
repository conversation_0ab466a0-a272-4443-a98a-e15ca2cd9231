"""Test PyGAD optimizers."""

import numpy as np
import pytest
from numpy.testing import assert_array_almost_equal as aaae

import optimagic as om
from optimagic.config import IS_PYGAD_INSTALLED
from optimagic.exceptions import NotInstalledError
from optimagic.optimizers.pygad_optimizers import PyGADGA
from optimagic.optimizers.pygad_new import (
    determine_effective_batch_size,
    convert_optimagic_stopping_criteria_to_pygad,
)


# Skip all tests if PyGAD is not installed
pytestmark = pytest.mark.skipif(
    not IS_PYGAD_INSTALLED, reason="PyGAD not installed"
)


def sphere_function(x):
    """Simple sphere function for testing: f(x) = sum(x^2)."""
    return np.sum(x**2)


def rosenbrock_function(x):
    """Rosenbrock function for testing."""
    return np.sum(100.0 * (x[1:] - x[:-1]**2)**2 + (1 - x[:-1])**2)


class TestPyGADGA:
    """Test the PyGAD genetic algorithm optimizer."""

    def test_basic_optimization_sphere(self):
        """Test basic optimization on sphere function."""
        start_params = np.array([2.0, 3.0])
        bounds = om.Bounds(lower=np.array([-5.0, -5.0]), upper=np.array([5.0, 5.0]))
        
        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
            algo_options={
                "stopping_maxiter": 50,
                "population_size": 20,
                "seed": 42,
            },
        )
        
        # Check that optimization was successful
        assert result.success is True
        assert result.fun < 1.0  # Should be close to 0
        assert len(result.params) == 2
        
        # Check that result is close to optimum (0, 0)
        aaae(result.params, np.array([0.0, 0.0]), decimal=1)

    def test_basic_optimization_rosenbrock(self):
        """Test basic optimization on Rosenbrock function."""
        start_params = np.array([0.0, 0.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))
        
        result = om.minimize(
            fun=rosenbrock_function,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
            algo_options={
                "stopping_maxiter": 100,
                "population_size": 50,
                "seed": 42,
            },
        )
        
        # Check that optimization was successful
        assert result.success is True
        assert result.fun < 10.0  # Should be reasonably close to 0
        assert len(result.params) == 2

    def test_maximize_function(self):
        """Test maximization (negative sphere function)."""
        def negative_sphere(x):
            return -np.sum(x**2)
        
        start_params = np.array([0.5, 0.5])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))
        
        result = om.maximize(
            fun=negative_sphere,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
            algo_options={
                "stopping_maxiter": 50,
                "population_size": 20,
                "seed": 42,
            },
        )
        
        # Check that optimization was successful
        assert result.success is True
        # For maximization, should find points near the bounds
        assert abs(result.fun) > 1.0  # Should be away from 0

    def test_bounds_handling(self):
        """Test that bounds are properly handled."""
        start_params = np.array([0.0, 0.0])
        bounds = om.Bounds(lower=np.array([-1.0, -1.0]), upper=np.array([1.0, 1.0]))
        
        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
            algo_options={
                "stopping_maxiter": 30,
                "population_size": 15,
                "seed": 42,
            },
        )
        
        # Check that result is within bounds
        assert np.all(result.params >= bounds.lower)
        assert np.all(result.params <= bounds.upper)

    def test_parameter_validation(self):
        """Test that PyGAD handles parameter validation."""
        # PyGAD has extensive built-in validation, so we just test that
        # invalid parameters are caught when the GA instance is created
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        # Test with invalid stopping_maxiter - should be caught by PyGAD
        with pytest.raises((ValueError, TypeError)):
            om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad_ga",
                bounds=bounds,
                algo_options={"stopping_maxiter": 0}
            )

    def test_bounds_required(self):
        """Test that bounds are required."""
        start_params = np.array([1.0, 1.0])
        
        with pytest.raises(ValueError, match="pygad_ga requires bounds"):
            om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad_ga",
                # No bounds provided
            )

    def test_finite_bounds_required(self):
        """Test that finite bounds are required."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(
            lower=np.array([-np.inf, -5.0]), 
            upper=np.array([5.0, np.inf])
        )
        
        # Should work but issue a warning about infinite bounds
        with pytest.warns(UserWarning, match="Infinite bounds have been replaced"):
            result = om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad_ga",
                bounds=bounds,
                algo_options={
                    "stopping_maxiter": 10,
                    "population_size": 10,
                    "seed": 42,
                },
            )
        
        # Should still work
        assert result.success is not None

    def test_different_selection_types(self):
        """Test different parent selection types."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))
        
        selection_types = ["sss", "rws", "sus", "rank", "random", "tournament"]
        
        for selection_type in selection_types:
            result = om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad_ga",
                bounds=bounds,
                algo_options={
                    "stopping_maxiter": 20,
                    "population_size": 15,
                    "parent_selection_type": selection_type,
                    "seed": 42,
                },
            )
            
            # Should complete without error
            assert result.success is not None
            assert len(result.params) == 2

    def test_different_crossover_types(self):
        """Test different crossover types."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))
        
        crossover_types = ["single_point", "two_points", "uniform", "scattered"]
        
        for crossover_type in crossover_types:
            result = om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad_ga",
                bounds=bounds,
                algo_options={
                    "stopping_maxiter": 20,
                    "population_size": 15,
                    "crossover_type": crossover_type,
                    "seed": 42,
                },
            )
            
            # Should complete without error
            assert result.success is not None
            assert len(result.params) == 2

    def test_different_mutation_types(self):
        """Test different mutation types."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        mutation_types = ["random", "swap", "inversion", "scramble"]

        for mutation_type in mutation_types:
            result = om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad_ga",
                bounds=bounds,
                algo_options={
                    "stopping_maxiter": 20,
                    "population_size": 15,
                    "mutation_type": mutation_type,
                    "seed": 42,
                },
            )

            # Should complete without error
            assert result.success is not None
            assert len(result.params) == 2

    def test_initial_population_parameter(self):
        """Test custom initial population parameter."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        # Create custom initial population
        custom_pop = np.array([
            [0.5, 0.5],
            [1.0, 1.0],
            [-0.5, -0.5],
            [0.0, 0.0],
            [1.5, -1.5]
        ])

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
            algo_options={
                "initial_population": custom_pop,
                "stopping_maxiter": 20,
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2

    def test_advanced_parameters(self):
        """Test advanced PyGAD parameters."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
            algo_options={
                "stopping_maxiter": 30,
                "population_size": 20,
                "mutation_by_replacement": True,
                "mutation_num_genes": 1,
                "allow_duplicate_genes": False,
                "fitness_batch_size": 5,
                "save_best_solutions": True,
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2

    def test_stop_criteria_parameter(self):
        """Test stop criteria parameter."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
            algo_options={
                "stopping_maxiter": 100,  # High limit
                "population_size": 20,
                "stop_criteria": ["reach_0.1", "saturate_10"],  # Should stop early
                "seed": 42,
            },
        )

        # Should complete without error and likely stop early
        assert result.success is not None
        assert len(result.params) == 2
        # Should stop before max iterations due to stop criteria
        assert result.n_iterations <= 100

    def test_optimagic_stopping_criteria_conversion(self):
        """Test conversion of Optimagic stopping criteria to PyGAD format."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        # Test with Optimagic stopping criteria
        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "population_size": 20,
                "stopping_maxiter": 50,  # Should limit generations
                "convergence_ftol_abs": 0.1,  # Should convert to reach criterion
                "convergence_ftol_rel": 1e-6,  # Should convert to saturate criterion
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2
        # Should respect stopping_maxiter
        assert result.n_iterations <= 50

    def test_stopping_maxfun_conversion(self):
        """Test conversion of stopping_maxfun to num_generations."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        # Test with stopping_maxfun only
        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "population_size": 10,
                "stopping_maxfun": 100,  # Should convert to ~10 generations
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2
        # Should have reasonable number of generations based on maxfun
        assert result.n_iterations <= 15  # Some buffer for the approximation

    def test_stopping_criteria_conversion_function(self):
        """Test the stopping criteria conversion function directly."""
        # Test with no criteria
        result = convert_optimagic_stopping_criteria_to_pygad()
        assert result is None

        # Test with user criteria (should take precedence)
        result = convert_optimagic_stopping_criteria_to_pygad(
            convergence_ftol_abs=0.1,
            user_stop_criteria=["reach_0.5", "saturate_5"]
        )
        assert result == ["reach_0.5", "saturate_5"]

        # Test with absolute tolerance
        result = convert_optimagic_stopping_criteria_to_pygad(
            convergence_ftol_abs=0.01
        )
        assert result == ["reach_-0.01"]

        # Test with relative tolerance
        result = convert_optimagic_stopping_criteria_to_pygad(
            convergence_ftol_rel=1e-6
        )
        assert result == ["saturate_10"]

        # Test with both tolerances
        result = convert_optimagic_stopping_criteria_to_pygad(
            convergence_ftol_abs=0.1,
            convergence_ftol_rel=1e-6
        )
        assert result == ["reach_-0.1", "saturate_10"]

        # Test with string user criteria
        result = convert_optimagic_stopping_criteria_to_pygad(
            user_stop_criteria="reach_1.0"
        )
        assert result == ["reach_1.0"]

    def test_result_attributes(self):
        """Test that result has all expected attributes."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
            algo_options={
                "stopping_maxiter": 20,
                "population_size": 15,
                "seed": 42,
            },
        )

        # Check that result has expected attributes
        assert result.success is not None
        assert len(result.params) == 2

    def test_gene_constraint_and_sample_size(self):
        """Test gene_constraint and sample_size parameters."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        # Define constraint functions
        def constraint1(solution, values):
            return [val for val in values if val < 1.5]

        def constraint2(solution, values):
            return [val for val in values if val > solution[0] - 0.5]

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 20,
                "population_size": 15,
                "gene_constraint": [constraint1, constraint2],
                "sample_size": 200,
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2

    def test_multi_objective_optimization(self):
        """Test multi-objective optimization with NSGA-II."""
        def multi_objective_function(x):
            # Two conflicting objectives
            obj1 = np.sum(x**2)  # Minimize distance from origin
            obj2 = np.sum((x - 1)**2)  # Minimize distance from (1,1)
            return [obj1, obj2]

        start_params = np.array([0.5, 0.5])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        result = om.minimize(
            fun=multi_objective_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 30,
                "population_size": 20,
                "parent_selection_type": "nsga2",
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2

        # Check that multi-objective information is stored
        assert result.algorithm_output is not None
        assert "multi_objective_fitness" in result.algorithm_output
        assert "n_objectives" in result.algorithm_output
        assert result.algorithm_output["n_objectives"] == 2
        
        # Check that all expected attributes are present
        assert hasattr(result, 'params')
        assert hasattr(result, 'fun')
        assert hasattr(result, 'success')
        assert hasattr(result, 'message')
        assert hasattr(result, 'n_fun_evals')
        assert hasattr(result, 'n_iterations')
        
        # Check types
        assert isinstance(result.params, np.ndarray)
        assert isinstance(result.fun, (int, float))
        assert isinstance(result.success, bool)
        assert isinstance(result.message, str)
        assert isinstance(result.n_fun_evals, (int, type(None)))
        assert isinstance(result.n_iterations, (int, type(None)))


# Test data for determine_effective_batch_size function
batch_size_test_cases = [
    # (fitness_batch_size, n_cores, expected_result, description)
    (None, 1, None, "single_threaded_none"),
    (5, 1, 5, "single_threaded_user_defined"),
    (None, 4, 4, "auto_sizing_parallel"),
    (None, 8, 8, "auto_sizing_large"),
    (8, 4, 8, "user_defined_efficient"),
    (12, 4, 12, "user_defined_large"),
    (4, 4, 4, "user_defined_equal"),
    (None, 0, None, "edge_case_zero_cores"),
    (None, 100, 100, "edge_case_large_cores"),
]

warning_test_cases = [
    # (fitness_batch_size, n_cores, should_warn, description)
    (2, 4, True, "inefficient_small_batch"),
    (1, 8, True, "inefficient_very_small"),
    (0, 4, True, "edge_case_zero_batch"),
    (4, 4, False, "efficient_equal"),
    (8, 4, False, "efficient_large_batch"),
    (None, 4, False, "auto_sizing_no_warn"),
    (5, 1, False, "single_threaded_no_warn"),
]


@pytest.mark.parametrize("fitness_batch_size,n_cores,expected,description", batch_size_test_cases)
def test_determine_effective_batch_size(fitness_batch_size, n_cores, expected, description):
    """Test determine_effective_batch_size function with various input combinations."""
    result = determine_effective_batch_size(fitness_batch_size, n_cores)
    assert result == expected, f"Failed for case: {description}"


@pytest.mark.parametrize("fitness_batch_size,n_cores,should_warn,description", warning_test_cases)
def test_determine_effective_batch_size_warnings(fitness_batch_size, n_cores, should_warn, description):
    """Test warning behavior of determine_effective_batch_size function."""
    if should_warn:
        warning_pattern = f"fitness_batch_size \\({fitness_batch_size}\\) is smaller than n_cores \\({n_cores}\\)"
        with pytest.warns(UserWarning, match=warning_pattern):
            result = determine_effective_batch_size(fitness_batch_size, n_cores)
        assert result == fitness_batch_size, f"Should return user choice for case: {description}"
    else:
        with pytest.warns(None) as warning_list:
            result = determine_effective_batch_size(fitness_batch_size, n_cores)
        assert len(warning_list) == 0, f"Should not warn for case: {description}"


def test_determine_effective_batch_size_type_consistency():
    """Test that determine_effective_batch_size returns consistent types."""
    # Should return int when given int inputs
    result = determine_effective_batch_size(5, 2)
    assert isinstance(result, int)
    assert result == 5

    # Should return int when auto-sizing
    result = determine_effective_batch_size(None, 4)
    assert isinstance(result, int)
    assert result == 4

    # Should return None for single-threaded
    result = determine_effective_batch_size(None, 1)
    assert result is None


class TestPygadNewOptimizer:
    """Test the new PyGAD optimizer with user-defined functions."""

    def test_basic_optimization_new_optimizer(self):
        """Test basic optimization with new PyGAD optimizer."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 30,
                "population_size": 20,
                "seed": 42,
            },
        )

        assert result.success is True
        assert len(result.params) == 2
        assert result.fun < 1.0  # Should be close to optimum

    def test_custom_parent_selection_function(self):
        """Test custom parent selection function."""
        def custom_parent_selection(fitness, num_parents, ga_instance):
            # Simple elitist selection
            fitness_sorted_indices = np.argsort(fitness)[::-1]
            parent_indices = fitness_sorted_indices[:num_parents]
            parents = ga_instance.population[parent_indices].copy()
            return parents, parent_indices

        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 20,
                "population_size": 15,
                "parent_selection_type": custom_parent_selection,
                "seed": 42,
            },
        )

        assert result.success is True
        assert len(result.params) == 2

    def test_custom_crossover_function(self):
        """Test custom crossover function."""
        def custom_crossover(parents, offspring_size, ga_instance):
            num_offspring, num_genes = offspring_size
            offspring = []

            for _ in range(num_offspring):
                parent1_idx = np.random.randint(0, parents.shape[0])
                parent2_idx = np.random.randint(0, parents.shape[0])

                # Simple uniform crossover
                child = np.where(
                    np.random.random(num_genes) < 0.5,
                    parents[parent1_idx],
                    parents[parent2_idx]
                )
                offspring.append(child)

            return np.array(offspring)

        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 20,
                "population_size": 15,
                "crossover_type": custom_crossover,
                "seed": 42,
            },
        )

        assert result.success is True
        assert len(result.params) == 2

    def test_custom_mutation_function(self):
        """Test custom mutation function."""
        def custom_mutation(offspring, ga_instance):
            mutation_probability = 0.1
            mutated_offspring = offspring.copy()

            for i in range(offspring.shape[0]):
                for j in range(offspring.shape[1]):
                    if np.random.random() < mutation_probability:
                        mutated_offspring[i, j] += np.random.normal(0, 0.1)

            return mutated_offspring

        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 20,
                "population_size": 15,
                "mutation_type": custom_mutation,
                "seed": 42,
            },
        )

        assert result.success is True
        assert len(result.params) == 2

    def test_gene_space_parameter(self):
        """Test gene space parameter functionality."""
        def discrete_objective(x):
            # Objective function for discrete optimization
            return (x[0] - 5)**2 + (x[1] - 2)**2

        start_params = np.array([3, 1])
        bounds = om.Bounds(lower=[0, 0], upper=[10, 5])

        result = om.minimize(
            fun=discrete_objective,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 30,
                "population_size": 20,
                "gene_space": [
                    range(0, 11),  # First gene: integers 0-10
                    range(0, 6),   # Second gene: integers 0-5
                ],
                "seed": 42,
            },
        )

        assert result.success is True
        assert len(result.params) == 2

    def test_parallel_processing_with_n_cores(self):
        """Test parallel processing using n_cores parameter."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        # Test with n_cores > 1 (should auto-set fitness_batch_size)
        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 10,
                "population_size": 20,
                "n_cores": 2,  # Should enable parallel processing
                "seed": 42,
            },
        )

        assert result.success is True
        assert len(result.params) == 2

    def test_parallel_processing_with_batch_size(self):
        """Test parallel processing using fitness_batch_size."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        # Test with batch processing
        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 10,
                "population_size": 20,
                "fitness_batch_size": 5,  # Process 5 solutions in parallel
                "n_cores": 2,
                "seed": 42,
            },
        )

        assert result.success is True
        assert len(result.params) == 2

    def test_no_parallel_processing(self):
        """Test that non-batch processing works correctly."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 20,
                "population_size": 15,
                "fitness_batch_size": None,  # No batching
                "seed": 42,
            },
        )

        assert result.success is True
        assert len(result.params) == 2

    def test_batch_size_one_equivalent_to_no_batching(self):
        """Test that fitness_batch_size=1 is equivalent to no batching."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        # Test with batch_size=1
        result1 = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 15,
                "population_size": 10,
                "fitness_batch_size": 1,
                "seed": 42,
            },
        )

        # Test with no batching
        result2 = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 15,
                "population_size": 10,
                "fitness_batch_size": None,
                "seed": 42,
            },
        )

        # Both should succeed and give similar results
        assert result1.success is True
        assert result2.success is True
        # Results should be very similar (same seed, same algorithm)
        np.testing.assert_allclose(result1.params, result2.params, rtol=0.1)

    def test_result_processing(self):
        """Test that result processing works correctly."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 15,
                "population_size": 10,
                "seed": 42,
            },
        )

        # Check that all required result fields are present
        assert hasattr(result, 'params')
        assert hasattr(result, 'fun')
        assert hasattr(result, 'success')
        assert hasattr(result, 'message')
        assert hasattr(result, 'n_fun_evals')

        # Check that success is boolean
        assert isinstance(result.success, bool)

        # Check that message is a string
        assert isinstance(result.message, str)

        # Check that message contains generation information
        assert "generation" in result.message.lower()

        # Check that function evaluations is reasonable
        assert result.n_fun_evals > 0
        assert result.n_fun_evals <= 15 * 10  # max generations * population size


@pytest.mark.skipif(IS_PYGAD_INSTALLED, reason="PyGAD is installed")
def test_not_installed_error():
    """Test that appropriate error is raised when PyGAD is not installed."""
    start_params = np.array([1.0, 1.0])
    bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

    with pytest.raises(NotInstalledError, match="pygad_ga optimizer requires the 'pygad' package"):
        om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad_ga",
            bounds=bounds,
        )
