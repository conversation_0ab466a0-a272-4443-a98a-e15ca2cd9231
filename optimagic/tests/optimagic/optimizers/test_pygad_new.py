"""Tests for the new PyGAD optimizer implementation."""

import numpy as np
import pytest

import optimagic as om
from optimagic.config import IS_PYGAD_INSTALLED


def sphere_function(x):
    """Simple sphere function for testing."""
    return np.sum(x**2)


@pytest.mark.skipif(not IS_PYGAD_INSTALLED, reason="PyGAD not installed")
class TestPygadNew:
    """Test the new PyGAD optimizer implementation."""

    def test_basic_optimization(self):
        """Test basic optimization functionality."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 20,
                "population_size": 10,
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2

    def test_initial_population_validation_empty(self):
        """Test that empty initial population raises ValueError."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        with pytest.raises(ValueError, match="initial_population cannot be empty"):
            om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad",
                bounds=bounds,
                algo_options={
                    "initial_population": np.array([]),
                    "num_generations": 10,
                },
            )

    def test_initial_population_validation_wrong_dimensions(self):
        """Test that 1D initial population raises ValueError."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        with pytest.raises(ValueError, match="initial_population must be a 2D array"):
            om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad",
                bounds=bounds,
                algo_options={
                    "initial_population": np.array([1.0, 2.0, 3.0]),  # 1D array
                    "num_generations": 10,
                },
            )

    def test_initial_population_validation_gene_mismatch(self):
        """Test that initial population with wrong number of genes raises ValueError."""
        start_params = np.array([1.0, 1.0])  # 2 parameters
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        # Create initial population with 3 genes instead of 2
        wrong_initial_pop = np.array([
            [0.5, 0.5, 0.5],  # 3 genes
            [1.0, 1.0, 1.0],  # 3 genes
        ])

        with pytest.raises(ValueError, match="Initial population has 3 genes but problem has 2 parameters"):
            om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad",
                bounds=bounds,
                algo_options={
                    "initial_population": wrong_initial_pop,
                    "num_generations": 10,
                },
            )

    def test_initial_population_valid(self):
        """Test that valid initial population works correctly."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        # Create valid initial population
        valid_initial_pop = np.array([
            [0.5, 0.5],
            [1.0, 1.0],
            [-0.5, -0.5],
            [0.0, 0.0],
        ])

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "initial_population": valid_initial_pop,
                "num_generations": 10,
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2

    def test_population_size_calculation(self):
        """Test that population size is calculated correctly."""
        start_params = np.array([1.0, 1.0, 1.0])  # 3 parameters
        bounds = om.Bounds(lower=np.array([-2.0, -2.0, -2.0]), upper=np.array([2.0, 2.0, 2.0]))

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "population_size": None,  # Should be calculated automatically
                "num_generations": 10,
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 3

    def test_finite_bounds_required(self):
        """Test that finite bounds are required."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(
            lower=np.array([-np.inf, -5.0]), 
            upper=np.array([5.0, np.inf])
        )
        
        with pytest.raises(ValueError, match="pygad_pygad requires finite bounds"):
            om.minimize(
                fun=sphere_function,
                params=start_params,
                algorithm="pygad",
                bounds=bounds,
                algo_options={
                    "num_generations": 10,
                    "population_size": 10,
                    "seed": 42,
                },
            )

    def test_parallel_processing_parameters(self):
        """Test parallel processing parameter handling."""
        start_params = np.array([1.0, 1.0])
        bounds = om.Bounds(lower=np.array([-2.0, -2.0]), upper=np.array([2.0, 2.0]))

        result = om.minimize(
            fun=sphere_function,
            params=start_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 10,
                "population_size": 10,
                "n_cores": 2,
                "fitness_batch_size": 4,
                "seed": 42,
            },
        )

        # Should complete without error
        assert result.success is not None
        assert len(result.params) == 2
