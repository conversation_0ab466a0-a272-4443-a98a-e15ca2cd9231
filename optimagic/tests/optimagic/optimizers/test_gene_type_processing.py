"""Test gene type processing functionality for PyGAD optimizer."""

import numpy as np
import pytest
from numpy.testing import assert_array_almost_equal as aaae

from optimagic.config import IS_PYGAD_INSTALLED
from optimagic.optimizers.pygad_new import _apply_gene_type_to_population

# Skip all tests if PyGAD is not installed
pytestmark = pytest.mark.skipif(
    not IS_PYGAD_INSTALLED, reason="PyGAD not installed"
)


class TestApplyGeneTypeToPopulation:
    """Test the _apply_gene_type_to_population function."""

    def test_single_int_type(self):
        """Test applying single integer type to entire population."""
        population = np.array([
            [1.7, 2.3, 3.9],
            [4.1, 5.8, 6.2],
            [7.5, 8.1, 9.6]
        ])
        
        result = _apply_gene_type_to_population(population, int)
        expected = np.array([
            [1, 2, 3],
            [4, 5, 6],
            [7, 8, 9]
        ])
        
        aaae(result, expected)
        assert result.dtype == int

    def test_single_float_type(self):
        """Test applying single float type to entire population."""
        population = np.array([
            [1, 2, 3],
            [4, 5, 6]
        ], dtype=int)
        
        result = _apply_gene_type_to_population(population, float)
        expected = np.array([
            [1.0, 2.0, 3.0],
            [4.0, 5.0, 6.0]
        ])
        
        aaae(result, expected)
        assert result.dtype == float

    def test_numpy_types(self):
        """Test applying numpy-specific types."""
        population = np.array([[1.5, 2.5, 3.5]])
        
        # Test np.int32
        result_int32 = _apply_gene_type_to_population(population, np.int32)
        assert result_int32.dtype == np.int32
        aaae(result_int32, [[1, 2, 3]])
        
        # Test np.float32
        result_float32 = _apply_gene_type_to_population(population, np.float32)
        assert result_float32.dtype == np.float32
        aaae(result_float32, [[1.5, 2.5, 3.5]])

    def test_type_with_precision_float(self):
        """Test applying float type with precision."""
        population = np.array([
            [1.789, 2.345, 3.987],
            [4.123, 5.876, 6.234]
        ])
        
        # Test with 1 decimal precision
        result1 = _apply_gene_type_to_population(population, [float, 1])
        expected1 = np.array([
            [1.8, 2.3, 4.0],
            [4.1, 5.9, 6.2]
        ])
        aaae(result1, expected1, decimal=1)
        
        # Test with 2 decimal precision
        result2 = _apply_gene_type_to_population(population, [float, 2])
        expected2 = np.array([
            [1.79, 2.35, 3.99],
            [4.12, 5.88, 6.23]
        ])
        aaae(result2, expected2, decimal=2)

    def test_type_with_none_precision(self):
        """Test applying type with None precision (equivalent to single type)."""
        population = np.array([[1.5, 2.5, 3.5]])
        
        # [int, None] should be equivalent to int
        result_int = _apply_gene_type_to_population(population, [int, None])
        expected_int = _apply_gene_type_to_population(population, int)
        aaae(result_int, expected_int)
        
        # [float, None] should be equivalent to float
        result_float = _apply_gene_type_to_population(population, [float, None])
        expected_float = _apply_gene_type_to_population(population, float)
        aaae(result_float, expected_float)

    def test_mixed_types_per_gene(self):
        """Test applying different types per gene."""
        population = np.array([
            [1.7, 2.345, 3.987],
            [4.1, 5.876, 6.234]
        ])
        
        # Mixed types: int, float with 2 decimals, float
        gene_types = [int, [float, 2], float]
        result = _apply_gene_type_to_population(population, gene_types)
        
        # Check that result has object dtype for mixed types
        assert result.dtype == object
        
        # Check first column (int)
        assert result[0, 0] == 1
        assert result[1, 0] == 4
        assert isinstance(result[0, 0], (int, np.integer))
        
        # Check second column (float with 2 decimals)
        assert abs(result[0, 1] - 2.35) < 0.001
        assert abs(result[1, 1] - 5.88) < 0.001
        
        # Check third column (float)
        assert abs(result[0, 2] - 3.987) < 0.001
        assert abs(result[1, 2] - 6.234) < 0.001

    def test_complex_mixed_types(self):
        """Test complex mixed type specifications."""
        population = np.array([
            [1.789, 2.345, 3.987, 4.123],
            [5.876, 6.234, 7.456, 8.789]
        ])
        
        # Complex mixed types
        gene_types = [int, [float, 1], [np.int32, None], [np.float32, 3]]
        result = _apply_gene_type_to_population(population, gene_types)
        
        # Check each column
        assert result[0, 0] == 1  # int
        assert abs(result[0, 1] - 2.3) < 0.01  # float with 1 decimal
        assert result[0, 2] == 3  # np.int32
        assert abs(result[0, 3] - 4.123) < 0.001  # np.float32 with 3 decimals

    def test_invalid_precision_for_int(self):
        """Test that integer types cannot have precision."""
        population = np.array([[1.5, 2.5]])
        
        with pytest.raises(ValueError, match="Integer types cannot have precision"):
            _apply_gene_type_to_population(population, [int, 2])
        
        with pytest.raises(ValueError, match="Integer types cannot have precision"):
            _apply_gene_type_to_population(population, [np.int32, 1])

    def test_invalid_gene_type_specification(self):
        """Test invalid gene type specifications."""
        population = np.array([[1.0, 2.0]])
        
        # Invalid single type
        with pytest.raises(ValueError, match="Invalid gene_type"):
            _apply_gene_type_to_population(population, "invalid")
        
        # Wrong length for mixed types (3 types for 2 genes)
        with pytest.raises(ValueError, match="Invalid gene_type"):
            _apply_gene_type_to_population(population, [int, float, int])
        
        # Invalid type in mixed specification
        with pytest.raises(ValueError, match="Invalid gene type at index"):
            _apply_gene_type_to_population(population, [int, "invalid"])
        
        # Invalid [type, precision] format
        with pytest.raises(ValueError, match="Invalid gene type at index"):
            _apply_gene_type_to_population(population, [int, [float, 1, "extra"]])

    def test_empty_population(self):
        """Test with empty population."""
        population = np.array([]).reshape(0, 2)
        
        result = _apply_gene_type_to_population(population, int)
        assert result.shape == (0, 2)
        assert result.dtype == int

    def test_single_gene_population(self):
        """Test with single gene population."""
        population = np.array([[1.5], [2.7], [3.9]])
        
        result = _apply_gene_type_to_population(population, int)
        expected = np.array([[1], [2], [3]])
        aaae(result, expected)

    def test_large_population(self):
        """Test with larger population to ensure performance."""
        # Create a larger population
        population = np.random.uniform(-10, 10, size=(100, 5))
        
        # Test single type
        result_int = _apply_gene_type_to_population(population, int)
        assert result_int.shape == (100, 5)
        assert result_int.dtype == int
        
        # Test mixed types
        gene_types = [int, float, [float, 1], [int, None], [float, 2]]
        result_mixed = _apply_gene_type_to_population(population, gene_types)
        assert result_mixed.shape == (100, 5)
        assert result_mixed.dtype == object

    def test_precision_edge_cases(self):
        """Test edge cases for precision handling."""
        population = np.array([[1.999, 2.001, 3.5]])
        
        # Test precision 0 (should round to integers but keep float type)
        result = _apply_gene_type_to_population(population, [float, 0])
        expected = np.array([[2.0, 2.0, 4.0]])
        aaae(result, expected)
        assert result.dtype == float

    def test_without_pygad_installed(self):
        """Test behavior when PyGAD is not installed."""
        # Temporarily mock IS_PYGAD_INSTALLED as False
        import optimagic.optimizers.pygad_new as pygad_module
        original_value = pygad_module.IS_PYGAD_INSTALLED
        
        try:
            pygad_module.IS_PYGAD_INSTALLED = False
            population = np.array([[1.5, 2.5]])
            
            # Should return original population unchanged
            result = _apply_gene_type_to_population(population, int)
            aaae(result, population)
            
        finally:
            # Restore original value
            pygad_module.IS_PYGAD_INSTALLED = original_value
