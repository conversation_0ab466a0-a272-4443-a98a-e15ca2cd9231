from dataclasses import dataclass
from typing import Any, Literal, Protocol, Callable
import warnings

import numpy as np
from numpy.typing import NDArray

from optimagic import mark
from optimagic.config import IS_PYGAD_INSTALLED
from optimagic.exceptions import NotInstalledError
from optimagic.optimization.algorithm import Algorithm, InternalOptimizeResult
from optimagic.optimization.internal_optimization_problem import (
    InternalOptimizationProblem,
)
from optimagic.optimization.algo_options import (
    get_population_size,
    STOPPING_MAXFUN_GLOBAL,
    STOPPING_MAXITER,
    CONVERGENCE_FTOL_ABS,
    CONVERGENCE_FTOL_REL,
)
from optimagic.typing import (
    AggregationLevel,
    NonNegativeFloat,
    PositiveFloat,
    PositiveInt,
)

if IS_PYGAD_INSTALLED:
    import pygad

# Default value for mutation_percent_genes parameter
DEFAULT_MUTATION_PERCENT_GENES = "default"

def determine_effective_batch_size(
    fitness_batch_size: int | None,
    n_cores: int
) -> int | None:
    """Determine the effective fitness_batch_size for parallel processing.

    Strategy:
    1. If fitness_batch_size is explicitly set, use it (with validation)
    2. If fitness_batch_size is None but n_cores > 1, auto-set to n_cores
    3. Validate that fitness_batch_size >= n_cores when both are set

    Args:
        fitness_batch_size: User-specified batch size or None
        n_cores: Number of cores for parallel processing

    Returns:
        Effective batch size for PyGAD, or None for single-threaded processing
    """
    if fitness_batch_size is not None:
        if fitness_batch_size < n_cores:
            warnings.warn(
                f"fitness_batch_size ({fitness_batch_size}) is smaller than "
                f"n_cores ({n_cores}). This may reduce parallel efficiency. "
                f"Consider setting fitness_batch_size >= n_cores."
            )
        return fitness_batch_size
    elif n_cores > 1:
        # Auto-set batch size to n_cores for efficient parallelization
        return n_cores
    else:
        # Single-threaded processing
        return None


def convert_optimagic_stopping_criteria_to_pygad(
    stopping_maxfun: int | None = None,
    stopping_maxiter: int | None = None,
    convergence_ftol_abs: float | None = None,
    convergence_ftol_rel: float | None = None,
    user_stop_criteria: str | list[str] | None = None,
) -> list[str] | None:
    """Convert Optimagic stopping criteria to PyGAD stop_criteria format.

    This function translates Optimagic's standard stopping criteria parameters
    into PyGAD's stop_criteria format, which uses strings like "reach_value"
    and "saturate_N".

    Args:
        stopping_maxfun: Maximum number of function evaluations (not directly
            supported by PyGAD, handled by num_generations)
        stopping_maxiter: Maximum number of iterations/generations
        convergence_ftol_abs: Absolute fitness tolerance for convergence
        convergence_ftol_rel: Relative fitness tolerance for convergence
        user_stop_criteria: User-provided PyGAD stop criteria (takes precedence)

    Returns:
        List of PyGAD stop_criteria strings, or None if no criteria specified

    Notes:
        - PyGAD's "reach_value" stops when fitness >= value (for maximization)
        - PyGAD's "saturate_N" stops after N generations without improvement
        - stopping_maxiter is handled by num_generations parameter, not stop_criteria
        - stopping_maxfun cannot be directly converted as PyGAD doesn't track function evals separately
    """
    criteria = []

    # User-provided criteria take precedence - if provided, use only those
    if user_stop_criteria is not None:
        if isinstance(user_stop_criteria, str):
            criteria.append(user_stop_criteria)
        elif isinstance(user_stop_criteria, list):
            criteria.extend(user_stop_criteria)
        # Return early if user criteria are provided
        return criteria if criteria else None

    # Convert absolute fitness tolerance to reach criterion
    # Note: PyGAD maximizes fitness, so we use negative values for minimization problems
    if convergence_ftol_abs is not None and convergence_ftol_abs > 0:
        # For minimization problems, we want to reach a small positive value
        # Since PyGAD maximizes, we convert minimization target to maximization
        reach_value = -convergence_ftol_abs  # Negative because we negate fitness for maximization
        criteria.append(f"reach_{reach_value}")

    # Convert relative fitness tolerance to saturation criterion
    # Use a reasonable default of 10 generations for saturation detection
    if convergence_ftol_rel is not None and convergence_ftol_rel > 0:
        # Relative tolerance suggests we want to stop when improvement is small
        # Map this to saturation detection over multiple generations
        saturation_generations = 10  # Default saturation period
        criteria.append(f"saturate_{saturation_generations}")

    # Note: stopping_maxiter is handled by the num_generations parameter in PyGAD
    # Note: stopping_maxfun cannot be directly mapped as PyGAD doesn't separate
    #       function evaluations from generations

    return criteria if criteria else None




# Protocol definitions for user-defined functions
class ParentSelectionFunction(Protocol):
    """Protocol for user-defined parent selection functions.

    Args:
        fitness: Array of fitness values for all solutions in the population.
        num_parents: Number of parents to select.
        ga_instance: The PyGAD GA instance.

    Returns:
        Tuple of (selected_parents, parent_indices) where:
        - selected_parents: 2D array of selected parent solutions
        - parent_indices: 1D array of indices of selected parents
    """
    def __call__(
        self,
        fitness: NDArray[np.float64],
        num_parents: int,
        ga_instance: Any
    ) -> tuple[NDArray[np.float64], NDArray[np.int_]]:
        ...


class CrossoverFunction(Protocol):
    """Protocol for user-defined crossover functions.

    Args:
        parents: 2D array of parent solutions selected for mating.
        offspring_size: Tuple (num_offspring, num_genes) specifying offspring dimensions.
        ga_instance: The PyGAD GA instance.

    Returns:
        2D array of offspring solutions.
    """
    def __call__(
        self,
        parents: NDArray[np.float64],
        offspring_size: tuple[int, int],
        ga_instance: Any
    ) -> NDArray[np.float64]:
        ...


class MutationFunction(Protocol):
    """Protocol for user-defined mutation functions.

    Args:
        offspring: 2D array of offspring solutions to be mutated.
        ga_instance: The PyGAD GA instance.

    Returns:
        2D array of mutated offspring solutions.
    """
    def __call__(
        self,
        offspring: NDArray[np.float64],
        ga_instance: Any
    ) -> NDArray[np.float64]:
        ...


class GeneConstraintFunction(Protocol):
    """Protocol for user-defined gene constraint functions.

    Args:
        solution: The solution where the gene exists.
        values: List or array of candidate values for the gene.

    Returns:
        List or array of filtered values that meet the gene constraint.
    """
    def __call__(
        self,
        solution: NDArray[np.float64],
        values: list[float] | NDArray[np.float64]
    ) -> list[float] | NDArray[np.float64]:
        ...


def _process_pygad_result(ga_instance: Any) -> InternalOptimizeResult:
    """Process PyGAD result into the standard InternalOptimizeResult format.

    Args:
        ga_instance: The PyGAD instance after running the optimization

    Returns:
        InternalOptimizeResult: Standardized optimization result
    """
    # Get the best solution
    best_solution, best_fitness, _ = ga_instance.best_solution()

    # Check if this is multi-objective optimization
    is_multi_objective = isinstance(best_fitness, (list, tuple, np.ndarray)) and len(best_fitness) > 1

    # Check if run completed successfully
    success = ga_instance.run_completed

    # Create appropriate message
    if success:
        message = f"Optimization terminated successfully after {ga_instance.generations_completed} generations."
    else:
        message = f"Optimization failed to complete. Only {ga_instance.generations_completed} generations completed."

    # Prepare algorithm output for multi-objective information
    algorithm_output = {}

    if is_multi_objective:
        # For multi-objective, use the first objective as the primary criterion
        # and store all objectives in algorithm_output
        best_criterion = -float(best_fitness[0])  # Convert back from maximization
        algorithm_output["multi_objective_fitness"] = [-float(f) for f in best_fitness]
        algorithm_output["n_objectives"] = len(best_fitness)

        # Add pareto front information if available
        if hasattr(ga_instance, 'pareto_fronts') and ga_instance.pareto_fronts:
            algorithm_output["pareto_fronts"] = ga_instance.pareto_fronts

        message += f" Multi-objective optimization with {len(best_fitness)} objectives."
    else:
        # Single objective optimization
        best_criterion = -float(best_fitness)  # Convert back from maximization

    return InternalOptimizeResult(
        x=best_solution,
        fun=best_criterion,
        success=success,
        message=message,
        n_fun_evals=int(ga_instance.generations_completed * ga_instance.pop_size[0]),
        info=algorithm_output if algorithm_output else None,
    )


@mark.minimizer(
    name="pygad",
    solver_type=AggregationLevel.SCALAR,
    is_available=IS_PYGAD_INSTALLED,
    is_global=True,
    needs_jac=False,
    needs_hess=False,
    supports_parallelism=True,
    supports_bounds=True,
    supports_linear_constraints=False,
    supports_nonlinear_constraints=False,
    disable_history=False,
)
@dataclass(frozen=True)
class Pygad(Algorithm):
    """PyGAD (Python Genetic Algorithm) optimizer.

    This optimizer implements genetic algorithms using the PyGAD library with support
    for parallel processing through optimagic's batch evaluation system and
    multi-objective optimization using NSGA-II.

    Multi-Objective Optimization:
        The optimizer automatically detects multi-objective problems when the objective
        function returns a list, tuple, or array with multiple values. For multi-objective
        problems, use parent_selection_type="nsga2" or "tournament_nsga2" for best results.
        Results include pareto front information in algorithm_output.

    Parallel Processing:
        The optimizer supports parallel fitness evaluation when both n_cores > 1 and
        fitness_batch_size > 1. If fitness_batch_size is None but n_cores > 1, the
        batch size is automatically set to n_cores for optimal parallelization.

    Args:
        population_size: Number of solutions in the population. If None, determined
            automatically based on problem size.
        num_parents_mating: Number of solutions to be selected as parents.
        num_generations: Number of generations to run the algorithm.
        initial_population: Custom initial population. If provided, population_size
            is determined from this array.

        parent_selection_type: Type of parent selection ("sss", "rws", "sus", "rank",
            "random", "tournament", "nsga2", "tournament_nsga2") or custom function.
            Use "nsga2" or "tournament_nsga2" for multi-objective optimization.
        keep_parents: Number of parents to keep in next generation (-1 for all).
        keep_elitism: Number of best solutions to keep in next generation.
        K_tournament: Number of solutions in tournament selection.

        crossover_type: Type of crossover ("single_point", "two_points", "uniform",
            "scattered") or custom function, or None to disable.
        crossover_probability: Probability of selecting a solution for crossover.

        mutation_type: Type of mutation ("random", "swap", "inversion", "scramble",
            "adaptive") or custom function, or None to disable.
        mutation_probability: Probability of selecting a gene for mutation.
        mutation_percent_genes: Percentage of genes to mutate (default: "default" = 10%).
        mutation_num_genes: Number of genes to mutate.
        mutation_by_replacement: Whether to replace genes during random mutation.
        random_mutation_min_val: Minimum value for random mutation.
        random_mutation_max_val: Maximum value for random mutation.

        gene_space: Allowed values for genes (discrete optimization).
        allow_duplicate_genes: Whether to allow duplicate gene values.

        fitness_batch_size: Batch size for parallel fitness evaluation. If None and
            n_cores > 1, automatically set to n_cores.
        save_best_solutions: Whether to save best solutions during evolution.
        save_solutions: Whether to save all solutions during evolution.
        stop_criteria: Early stopping criteria in PyGAD format (e.g., "reach_0.1",
            "saturate_10"). If None, Optimagic stopping criteria are converted automatically.

        stopping_maxfun: Maximum number of function evaluations. Used to determine
            num_generations if not explicitly set.
        stopping_maxiter: Maximum number of iterations/generations. Overrides num_generations
            if both are specified.
        convergence_ftol_abs: Absolute fitness tolerance. Converted to PyGAD "reach" criterion.
        convergence_ftol_rel: Relative fitness tolerance. Converted to PyGAD "saturate" criterion.

        n_cores: Number of cores for parallel fitness evaluation. Default is 1.
            Note: The batch evaluator is controlled at the optimization problem level,
            not by individual optimizers.

        seed: Random seed for reproducibility.
        suppress_warnings: Whether to suppress PyGAD warnings.
    """

    population_size: PositiveInt | None = None
    num_parents_mating: PositiveInt | None = None
    num_generations: PositiveInt | None = None

    initial_population: NDArray[np.float64] | list[list[float]] | None = None

    parent_selection_type: (
        Literal["sss", "rws", "sus", "rank", "random", "tournament", "nsga2", "tournament_nsga2"] |
        ParentSelectionFunction
    ) = "sss"
    keep_parents: int = -1
    keep_elitism: PositiveInt = 1
    K_tournament: PositiveInt = 3

    crossover_type: (
        Literal["single_point", "two_points", "uniform", "scattered"] |
        CrossoverFunction |
        None
    ) = "single_point"
    crossover_probability: NonNegativeFloat | None = None

    mutation_type: (
        Literal["random", "swap", "inversion", "scramble", "adaptive"] |
        MutationFunction |
        None
    ) = "random"
    mutation_probability: (
        NonNegativeFloat |
        list[NonNegativeFloat] |
        tuple[NonNegativeFloat, NonNegativeFloat] |
        NDArray[np.float64] |
        None
    ) = None
    mutation_percent_genes: (
        PositiveFloat |
        str |
        list[PositiveFloat] |
        tuple[PositiveFloat, PositiveFloat] |
        NDArray[np.float64]
    ) = DEFAULT_MUTATION_PERCENT_GENES
    mutation_num_genes: (
        PositiveInt |
        list[PositiveInt] |
        tuple[PositiveInt, PositiveInt] |
        NDArray[np.int_] |
        None
    ) = None
    mutation_by_replacement: bool = False
    random_mutation_min_val: float | list[float] | NDArray[np.float64] = -1.0
    random_mutation_max_val: float | list[float] | NDArray[np.float64] = 1.0

    # Gene space and constraints
    gene_space: (
        list[Any] |
        dict[str, float] |
        range |
        NDArray[np.float64] |
        None
    ) = None
    allow_duplicate_genes: bool = True

    # Performance and monitoring
    fitness_batch_size: PositiveInt | None = None
    save_best_solutions: bool = False
    save_solutions: bool = False
    stop_criteria: (
        str |
        list[str] |
        None
    ) = None

    # Optimagic stopping criteria (converted to PyGAD format)
    stopping_maxfun: PositiveInt = STOPPING_MAXFUN_GLOBAL
    stopping_maxiter: PositiveInt = STOPPING_MAXITER
    convergence_ftol_abs: NonNegativeFloat = CONVERGENCE_FTOL_ABS
    convergence_ftol_rel: NonNegativeFloat = CONVERGENCE_FTOL_REL

    # Parallel processing parameters
    n_cores: PositiveInt = 1

    # System parameters
    seed: int | None = None
    suppress_warnings: bool = True

    def _validate_parameters(self) -> None:
        """Validate all parameters and issue warnings for conflicts."""

        # Warnings for user-defined functions overriding built-in parameters
        if callable(self.parent_selection_type):
            if self.K_tournament != 3:  # Default value
                warnings.warn(
                    f"You provided a custom parent_selection_type function but also specified "
                    f"K_tournament={self.K_tournament}. K_tournament will be ignored when using "
                    "a custom parent selection function."
                )

        if callable(self.crossover_type):
            if self.crossover_probability is not None:
                warnings.warn(
                    f"You provided a custom crossover_type function but also specified "
                    f"crossover_probability={self.crossover_probability}. crossover_probability "
                    "will be ignored when using a custom crossover function."
                )

        if callable(self.mutation_type):
            if self.mutation_probability is not None:
                warnings.warn(
                    "You provided a custom mutation_type function but also specified "
                    f"mutation_probability={self.mutation_probability}. mutation_probability "
                    "will be ignored when using a custom mutation function."
                )
            if self.mutation_percent_genes != "default":
                warnings.warn(
                    "You provided a custom mutation_type function but also specified "
                    f"mutation_percent_genes={self.mutation_percent_genes}. mutation_percent_genes "
                    "will be ignored when using a custom mutation function."
                )
            if self.mutation_num_genes is not None:
                warnings.warn(
                    "You provided a custom mutation_type function but also specified "
                    f"mutation_num_genes={self.mutation_num_genes}. mutation_num_genes "
                    "will be ignored when using a custom mutation function."
                )
            if (self.random_mutation_min_val != -1.0 or self.random_mutation_max_val != 1.0):
                warnings.warn(
                    "You provided a custom mutation_type function but also specified "
                    "random_mutation_min_val or random_mutation_max_val. These parameters "
                    "will be ignored when using a custom mutation function."
                )
            if self.mutation_by_replacement:
                warnings.warn(
                    "You provided a custom mutation_type function but also specified "
                    "mutation_by_replacement=True. This parameter will be ignored when "
                    "using a custom mutation function."
                )

        # Warnings for conflicts between built-in parameters
        if not callable(self.parent_selection_type) and self.K_tournament != 3 and self.parent_selection_type != "tournament":
            warnings.warn(
                f"You specified K_tournament={self.K_tournament} but "
                f"parent_selection_type is '{self.parent_selection_type}'. "
                "K_tournament is only used when parent_selection_type='tournament'. "
                "K_tournament will be ignored."
            )

        if not callable(self.crossover_type) and self.crossover_probability is not None and self.crossover_type is None:
            warnings.warn(
                f"You specified crossover_probability={self.crossover_probability} but "
                "crossover_type is None. crossover_probability has no effect when "
                "crossover is disabled."
            )

        if not callable(self.mutation_type) and (self.mutation_probability is not None and
            self.mutation_percent_genes != "default"):
            warnings.warn(
                "You specified both mutation_probability and mutation_percent_genes. "
                "mutation_percent_genes will be ignored when mutation_probability is set."
            )

        if not callable(self.mutation_type) and (self.mutation_num_genes is not None and
            self.mutation_probability is not None):
            warnings.warn(
                "You specified both mutation_num_genes and mutation_probability. "
                "mutation_num_genes will be ignored when mutation_probability is set."
            )

        if not callable(self.mutation_type) and (self.mutation_num_genes is not None and
            self.mutation_percent_genes != "default"):
            warnings.warn(
                "You specified both mutation_num_genes and mutation_percent_genes. "
                "mutation_percent_genes will be ignored when mutation_num_genes is set."
            )

        if not callable(self.mutation_type) and (self.random_mutation_min_val != -1.0 or self.random_mutation_max_val != 1.0) and self.mutation_type != "random":
            warnings.warn(
                f"You specified random_mutation_min_val or random_mutation_max_val but "
                f"mutation_type is '{self.mutation_type}'. These parameters are only used "
                "when mutation_type='random'. They will be ignored."
            )






    def _solve_internal_problem(
        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]
    ) -> InternalOptimizeResult:
        if not IS_PYGAD_INSTALLED:
            raise NotInstalledError(
                "The 'pygad_pygad' algorithm requires the pygad package to be "
                "installed. You can install it with 'pip install pygad'."
            )

        if (
            problem.bounds.lower is None
            or problem.bounds.upper is None
            or not np.isfinite(problem.bounds.lower).all()
            or not np.isfinite(problem.bounds.upper).all()
        ):
            raise ValueError("pygad_pygad requires finite bounds for all parameters.")

        # Validate all parameters and issue warnings
        self._validate_parameters()

        # Convert Optimagic stopping criteria to PyGAD format
        converted_stop_criteria = convert_optimagic_stopping_criteria_to_pygad(
            stopping_maxfun=self.stopping_maxfun,
            stopping_maxiter=self.stopping_maxiter,
            convergence_ftol_abs=self.convergence_ftol_abs,
            convergence_ftol_rel=self.convergence_ftol_rel,
            user_stop_criteria=self.stop_criteria,
        )

        # Determine effective num_generations from stopping criteria
        effective_num_generations = self.num_generations
        if effective_num_generations is None:
            # Use stopping_maxiter if available, otherwise use stopping_maxfun as approximation
            if self.stopping_maxiter and self.stopping_maxiter != STOPPING_MAXITER:
                effective_num_generations = self.stopping_maxiter
            elif self.stopping_maxfun and self.stopping_maxfun != STOPPING_MAXFUN_GLOBAL:
                # Rough approximation: assume population_size evaluations per generation
                population_size = get_population_size(
                    population_size=self.population_size, x=x0, lower_bound=10
                )
                effective_num_generations = max(1, self.stopping_maxfun // population_size)
            else:
                # Use a reasonable default
                effective_num_generations = 100

        # Determine effective fitness_batch_size for parallel processing
        effective_fitness_batch_size = determine_effective_batch_size(
            self.fitness_batch_size, self.n_cores
        )

        # Create fitness function based on whether batch processing is enabled
        # Enable batch processing only when both batch_size > 1 AND n_cores > 1
        if (effective_fitness_batch_size is not None and effective_fitness_batch_size > 1 and
            self.n_cores > 1):
            # Batch fitness function - uses optimagic's parallel processing
            def fitness_function(
                _ga_instance: Any,
                batch_solutions: NDArray[np.float64],
                _batch_indices: list[int] | NDArray[np.int_]
            ) -> list[float] | list[list[float]]:
                """Batch fitness function using optimagic's parallel processing."""
                # Convert batch_solutions to list of 1D arrays efficiently
                # Using list() on the array directly is more efficient than list comprehension
                solution_list = [solution for solution in batch_solutions]

                # Use optimagic's batch evaluation with proper n_cores
                batch_results = problem.batch_fun(solution_list, n_cores=self.n_cores)

                # Check if this is multi-objective (first result is iterable with multiple values)
                first_result = batch_results[0]
                if hasattr(first_result, '__len__') and len(first_result) > 1:
                    # Multi-objective: return list of lists, negate each objective for maximization
                    return [[-float(obj) for obj in result] for result in batch_results]
                else:
                    # Single objective: return list of floats, negate for maximization
                    return [-float(result) for result in batch_results]
        else:
            # Single solution fitness function
            def fitness_function(
                _ga_instance: Any,
                solution: NDArray[np.float64],
                _solution_idx: int
            ) -> float | list[float]:
                """Single solution fitness function."""
                result = problem.fun(solution)

                # Check if this is multi-objective
                if hasattr(result, '__len__') and len(result) > 1:
                    # Multi-objective: return list, negate each objective for maximization
                    return [-float(obj) for obj in result]
                else:
                    # Single objective: return float, negate for maximization
                    return -float(result)

        # Calculate population size
        population_size = get_population_size(
            population_size=self.population_size, x=x0, lower_bound=10
        )

        # Process initial population
        if self.initial_population is not None:
            initial_population = np.array(self.initial_population)

            # Validate initial population is not empty
            if initial_population.size == 0:
                raise ValueError("initial_population cannot be empty.")

            # Validate initial population dimensions
            if initial_population.ndim != 2:
                raise ValueError(
                    f"initial_population must be a 2D array, got {initial_population.ndim}D array."
                )

            population_size = len(initial_population)
            num_genes = initial_population.shape[1]

            # Validate that initial population dimensions match problem dimensions
            if num_genes != len(x0):
                raise ValueError(
                    f"Initial population has {num_genes} genes but problem has "
                    f"{len(x0)} parameters. They must match."
                )
        else:
            num_genes = len(x0)

            # Create initial population that includes the starting point
            initial_population = np.random.uniform(
                problem.bounds.lower,
                problem.bounds.upper,
                size=(population_size, num_genes)
            )

            # Ensure the first solution is the starting point
            initial_population[0] = x0

        # Set default num_parents_mating if not provided
        num_parents_mating = self.num_parents_mating
        if num_parents_mating is None:
            num_parents_mating = population_size // 2

        ga_instance = pygad.GA(
            num_generations=effective_num_generations,
            num_parents_mating=num_parents_mating,
            fitness_func=fitness_function,
            fitness_batch_size=effective_fitness_batch_size,  # Use effective batch size
            initial_population=initial_population,
            # Note: sol_per_pop and num_genes are automatically inferred from initial_population
            gene_space=self.gene_space,
            parent_selection_type=self.parent_selection_type,
            keep_parents=self.keep_parents,
            keep_elitism=self.keep_elitism,
            K_tournament=self.K_tournament,
            crossover_type=self.crossover_type,
            crossover_probability=self.crossover_probability,
            mutation_type=self.mutation_type,
            mutation_probability=self.mutation_probability,
            mutation_by_replacement=self.mutation_by_replacement,
            mutation_percent_genes=self.mutation_percent_genes,
            mutation_num_genes=self.mutation_num_genes,
            random_mutation_min_val=self.random_mutation_min_val,
            random_mutation_max_val=self.random_mutation_max_val,
            allow_duplicate_genes=self.allow_duplicate_genes,
            save_best_solutions=self.save_best_solutions,
            save_solutions=self.save_solutions,
            suppress_warnings=self.suppress_warnings,
            stop_criteria=converted_stop_criteria or self.stop_criteria,  # Use converted criteria
            parallel_processing=None,  # Always disable PyGAD's parallel processing
            random_seed=self.seed,
        )

        ga_instance.run()

        return _process_pygad_result(ga_instance)