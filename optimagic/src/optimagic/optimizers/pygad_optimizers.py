"""Implement PyGAD genetic algorithm optimizers."""

from __future__ import annotations

import warnings
from dataclasses import dataclass
from typing import Any, Literal, Union

import numpy as np
from numpy.typing import NDArray

from optimagic import mark
from optimagic.config import IS_PYGAD_INSTALLED
from optimagic.exceptions import NotInstalledError
from optimagic.optimization.algo_options import (
    get_population_size,
)
from optimagic.optimization.algorithm import Algorithm, InternalOptimizeResult
from optimagic.optimization.internal_optimization_problem import (
    InternalOptimizationProblem,
)
from optimagic.typing import (
    AggregationLevel,
    NonNegativeFloat,
    PositiveFloat,
    PositiveInt,
)

# Default values for PyGAD genetic algorithm
STOPPING_MAX_GENERATIONS = 100
DEFAULT_MUTATION_PERCENT_GENES = 10

if IS_PYGAD_INSTALLED:
    import pygad


@mark.minimizer(
    name="pygad_ga",
    solver_type=AggregationLevel.SCALAR,
    is_available=IS_PYGAD_INSTALLED,
    is_global=True,
    needs_jac=False,
    needs_hess=False,
    supports_parallelism=True,
    supports_bounds=True,
    supports_linear_constraints=False,
    supports_nonlinear_constraints=False,
    disable_history=False,
)
@dataclass(frozen=True)
class PyGADGA(Algorithm):
    """PyGAD Genetic Algorithm optimizer.

    This optimizer uses the PyGAD library to perform genetic algorithm optimization.
    PyGAD is a Python library for building genetic algorithms and training machine
    learning algorithms. It implements a standard genetic algorithm with various
    selection, crossover, and mutation operators.

    The genetic algorithm is a metaheuristic inspired by the process of natural
    selection. It maintains a population of candidate solutions and evolves them
    over generations using genetic operators like selection, crossover, and mutation.

    **Key Features:**
    - Global optimization capability
    - Handles bounded optimization problems
    - Supports parallel processing
    - Multiple selection, crossover, and mutation strategies
    - Elitism to preserve best solutions

    **Requirements:**
    - Requires finite bounds for all parameters
    - Does not support linear or nonlinear constraints
    - Best suited for problems where gradient information is not available

    Args:
        population_size: Number of solutions in the population. Must be positive integer.
            If None, calculated as 10 * (n_params + 1) with minimum of 10. Larger
            populations provide better exploration but require more function evaluations.
        num_parents_mating: Number of solutions selected as parents for mating. Must be
            positive integer ≤ population_size. If None, set to half of population_size.
        stopping_maxiter: Maximum number of generations (iterations). Must be positive
            integer. Default is 100. Each generation evaluates the entire population.
        initial_population: User-defined initial population as 2D array/list of shape
            (population_size, num_genes). Can be numpy array or nested list. If None,
            PyGAD creates random initial population using init_range_* parameters.
        init_range_low: Lower bound(s) for random initialization. Can be:
            - float: Same lower bound for all genes
            - list/array: Different lower bound per gene (length = num_genes)
            Default is -4.0. Only used if initial_population is None.
        init_range_high: Upper bound(s) for random initialization. Can be:
            - float: Same upper bound for all genes
            - list/array: Different upper bound per gene (length = num_genes)
            Default is 4.0. Only used if initial_population is None.
        gene_type: Data type(s) for genes. Can be:
            - Single type: int, float, numpy types (int8, int16, int32, int64, uint8, etc.)
            - List of types: Different type per gene
            - List of [type, precision]: For float types with precision
            Default is float. Affects precision and memory usage.
        parent_selection_type: Type of parent selection strategy:
            - "sss": Steady-state selection (default)
            - "rws": Roulette wheel selection
            - "sus": Stochastic universal selection
            - "rank": Rank-based selection
            - "random": Random selection
            - "tournament": Tournament selection (uses K_tournament parameter)
        keep_parents: Number of parents to keep in the next generation. -1 means
            keep all parents, 0 means keep no parents. Default is -1.
        keep_elitism: Number of best solutions to keep in the next generation.
            Default is 1. Helps preserve good solutions across generations.
        K_tournament: Number of solutions to compete in tournament selection.
            Only used when parent_selection_type="tournament". Default is 3.
        crossover_type: Type of crossover operation:
            - "single_point": Single-point crossover (default)
            - "two_points": Two-point crossover
            - "uniform": Uniform crossover
            - "scattered": Scattered crossover
        crossover_probability: Probability of applying crossover to a solution pair.
            If None, crossover is always applied. Should be between 0 and 1.
        mutation_type: Type of mutation operation. Can be:
            - "random": Random mutation (default) - adds/replaces with random values
            - "swap": Swap mutation - swaps positions of two genes
            - "inversion": Inversion mutation - reverses gene sequence
            - "scramble": Scramble mutation - randomly shuffles genes
            - "adaptive": Adaptive mutation - uses adaptive parameters
            - None: No mutation applied
        mutation_probability: Probability of mutating each gene (0-1). Can be:
            - float: Fixed probability for all genes
            - list/tuple of 2 floats: [min, max] for adaptive mutation
            - array: Different probability per gene
            If specified, takes precedence over mutation_percent_genes.
        mutation_percent_genes: Percentage of genes to mutate. Can be:
            - float: Percentage (0-100), default is 10
            - "default": Uses PyGAD default (10%)
            - list/tuple of 2 floats: [min%, max%] for adaptive mutation
            Only used if mutation_probability is None.
        mutation_num_genes: Number of genes to mutate. Can be:
            - int: Fixed number of genes to mutate
            - list/tuple of 2 ints: [min, max] for adaptive mutation
            Takes precedence over mutation_percent_genes if specified.
        mutation_by_replacement: Whether to replace gene values during random mutation
            (True) or add random values to existing ones (False). Only applies when
            mutation_type="random". Default is False.
        random_mutation_min_val: Minimum value for random mutation. Can be:
            - float: Same minimum for all genes
            - list/array: Different minimum per gene
            Default is -1.0. Used when mutation_type="random".
        random_mutation_max_val: Maximum value for random mutation. Can be:
            - float: Same maximum for all genes
            - list/array: Different maximum per gene
            Default is 1.0. Used when mutation_type="random".
        gene_space: Defines the space of possible gene values. Can be:
            - list: List of possible values for each gene (can be nested for per-gene spaces)
            - dict: {'low': min_val, 'high': max_val, 'step': step_size} for continuous space
            - range: Range of integer values
            - numpy array: Array of possible values
            - None: Use init_range_* parameters for initialization and mutation
            Overrides init_range_* parameters when specified.

            **Detailed Format Examples:**

            1. **Same space for all genes:**
               gene_space = [0.3, 5.2, -4, 8]  # All genes can only have these 4 values
               gene_space = range(10)          # All genes can have values 0-9
               gene_space = np.arange(0, 10, 0.5)  # All genes: 0, 0.5, 1.0, ..., 9.5

            2. **Different space per gene (nested list):**
               gene_space = [[0.4, -5], [0.5, -3.2, 8.2, -9], [1, 2, 3]]
               # Gene 0: can be 0.4 or -5
               # Gene 1: can be 0.5, -3.2, 8.2, or -9
               # Gene 2: can be 1, 2, or 3

            3. **Continuous ranges using dictionaries:**
               gene_space = {"low": 1, "high": 5}  # All genes: continuous range [1, 5)
               gene_space = [{"low": 0, "high": 10}, {"low": -5, "high": 5}]
               # Gene 0: continuous range [0, 10), Gene 1: continuous range [-5, 5)

            4. **Discrete ranges with step:**
               gene_space = {"low": 0, "high": 10, "step": 2}  # Values: 0, 2, 4, 6, 8

            5. **Mixed formats:**
               gene_space = [
                   [1, 2, 3],                    # Gene 0: discrete values
                   {"low": 0, "high": 1},        # Gene 1: continuous range
                   range(5, 10),                 # Gene 2: discrete range 5-9
                   None                          # Gene 3: use init_range_* params
               ]

            6. **Fixed gene values:**
               gene_space = [5, [1, 2, 3], {"low": 0, "high": 1}]
               # Gene 0: always 5, Gene 1: can be 1/2/3, Gene 2: continuous [0,1)
        allow_duplicate_genes: Whether to allow duplicate gene values within a solution.
            Default is True. Set to False to enforce unique gene values per solution.
        fitness_batch_size: Number of solutions to evaluate in a single batch. Can be:
            - None or 1: Evaluate solutions individually (default)
            - int > 1: Batch size for fitness evaluation (can improve performance)
            Must be ≤ population_size.
        save_best_solutions: Whether to save the best solution from each generation.
            Default is False. If True, accessible via ga_instance.best_solutions.
            Use with caution as it increases memory usage.
        save_solutions: Whether to save all solutions from each generation.
            Default is False. If True, accessible via ga_instance.solutions.
            Use with extreme caution as it can cause memory overflow.
        stop_criteria: Early stopping criteria. Can be:
            - str: Single criterion like "reach_0.01" or "saturate_10"
            - list: Multiple criteria like ["reach_0.01", "saturate_10"]
            - None: Only use stopping_maxiter (default)
            Format: "reach_value" stops when fitness ≥ value, "saturate_N" stops after N generations without improvement.
        seed: Random seed for reproducibility. If None, uses random initialization.
            Controls numpy.random and random module seeds.
        parallel_processing: Parallel processing configuration. Can be:
            - None: No parallel processing (default)
            - int: Number of threads to use (e.g., 4)
            - tuple/list: [type, count] where type is "process" or "thread" and count is:
              * int > 0: Number of workers
              * 0: No parallel processing
              * None: Use system default
        suppress_warnings: Whether to suppress PyGAD warning messages. Default is True.

    Examples:
        Basic usage with default parameters:

        >>> import optimagic as om
        >>> import numpy as np
        >>>
        >>> def sphere(x):
        ...     return np.sum(x**2)
        >>>
        >>> result = om.minimize(
        ...     fun=sphere,
        ...     params=np.array([2.0, 3.0]),
        ...     algorithm="pygad_ga",
        ...     bounds=om.Bounds(lower=[-5, -5], upper=[5, 5])
        ... )

        Advanced usage with proper type examples:

        >>> # Custom initial population (2D array/list)
        >>> custom_pop = np.array([[1.0, 2.0], [0.5, 1.5], [-1.0, 0.0]])
        >>> # Or as nested list: [[1.0, 2.0], [0.5, 1.5], [-1.0, 0.0]]
        >>>
        >>> result = om.minimize(
        ...     fun=sphere,
        ...     params=np.array([2.0, 3.0]),
        ...     algorithm="pygad_ga",
        ...     bounds=om.Bounds(lower=[-5, -5], upper=[5, 5]),
        ...     algo_options={
        ...         "initial_population": custom_pop,
        ...         "stopping_maxiter": 200,
        ...         "gene_type": float,  # or np.float32, int, etc.
        ...         "init_range_low": [-2.0, -3.0],  # Different per gene
        ...         "init_range_high": [2.0, 3.0],   # Different per gene
        ...         "mutation_probability": [0.05, 0.15],  # Adaptive mutation
        ...         "mutation_num_genes": 1,  # Mutate exactly 1 gene
        ...         "gene_space": [{"low": -5, "high": 5}, {"low": -3, "high": 3}],
        ...         "parallel_processing": ("thread", 4),  # 4 threads
        ...         "stop_criteria": ["reach_0.01", "saturate_15"],
        ...         "seed": 42
        ...     }
        ... )

    Note:
        This optimizer requires the PyGAD package to be installed:
        ``pip install pygad``

        For more information about PyGAD, visit: https://pygad.readthedocs.io/
    """

    # Core GA parameters
    population_size: PositiveInt | None = None
    num_parents_mating: PositiveInt | None = None
    stopping_maxiter: PositiveInt = STOPPING_MAX_GENERATIONS

    # Initial population parameters
    initial_population: NDArray[np.float64] | list[list[float]] | None = None
    init_range_low: float | list[float] | NDArray[np.float64] = -4.0
    init_range_high: float | list[float] | NDArray[np.float64] = 4.0
    gene_type: (
        type[int] | type[float] |
        type[np.int8] | type[np.int16] | type[np.int32] | type[np.int64] |
        type[np.uint] | type[np.uint8] | type[np.uint16] | type[np.uint32] | type[np.uint64] |
        type[np.float16] | type[np.float32] | type[np.float64] |
        list[type] | list[list[type | None]]
    ) = float

    # Selection parameters
    parent_selection_type: Literal[
        "sss", "rws", "sus", "rank", "random", "tournament"
    ] = "sss"
    keep_parents: int = -1
    keep_elitism: PositiveInt = 1
    K_tournament: PositiveInt = 3

    # Crossover parameters
    crossover_type: Literal[
        "single_point", "two_points", "uniform", "scattered"
    ] | None = "single_point"
    crossover_probability: NonNegativeFloat | None = None

    # Mutation parameters
    mutation_type: Literal[
        "random", "swap", "inversion", "scramble", "adaptive"
    ] | None = "random"
    mutation_probability: (
        NonNegativeFloat |
        list[NonNegativeFloat] |
        tuple[NonNegativeFloat, NonNegativeFloat] |
        NDArray[np.float64] |
        None
    ) = None
    mutation_percent_genes: (
        PositiveFloat |
        str |  # "default"
        list[PositiveFloat] |
        tuple[PositiveFloat, PositiveFloat] |
        NDArray[np.float64]
    ) = DEFAULT_MUTATION_PERCENT_GENES
    mutation_num_genes: (
        PositiveInt |
        list[PositiveInt] |
        tuple[PositiveInt, PositiveInt] |
        NDArray[np.int_] |
        None
    ) = None
    mutation_by_replacement: bool = False
    random_mutation_min_val: float | list[float] | NDArray[np.float64] = -1.0
    random_mutation_max_val: float | list[float] | NDArray[np.float64] = 1.0

    # Gene space and constraints
    gene_space: (
        list[Any] |  # List of possible values per gene
        dict[str, float] |  # Dict with 'low', 'high', optional 'step'
        range |  # Range of values
        NDArray[np.float64] |
        None
    ) = None
    allow_duplicate_genes: bool = True

    # Performance and monitoring
    fitness_batch_size: PositiveInt | None = None
    save_best_solutions: bool = False
    save_solutions: bool = False
    stop_criteria: (
        str |  # Single criterion like "reach_0.01"
        list[str] |  # Multiple criteria like ["reach_0.01", "saturate_10"]
        None
    ) = None

    # System parameters
    seed: int | None = None
    parallel_processing: (
        int |  # Number of threads
        tuple[Literal["process", "thread"], int | None] |  # [type, count]
        list[Union[Literal["process", "thread"], int | None]] |  # [type, count] as list
        None
    ) = None
    suppress_warnings: bool = True

    def _solve_internal_problem(
        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]
    ) -> InternalOptimizeResult:
        # Check if PyGAD is available
        if not IS_PYGAD_INSTALLED:
            raise NotInstalledError(
                "The pygad_ga optimizer requires the 'pygad' package to be "
                "installed. You can install it with `pip install pygad`. "
                "Visit https://pygad.readthedocs.io/ for more detailed "
                "installation instructions."
            )

        # Validate bounds - PyGAD requires finite bounds
        if problem.bounds is None or problem.bounds.lower is None or problem.bounds.upper is None:
            raise ValueError("pygad_ga requires finite bounds for all parameters.")

        lower_bounds = problem.bounds.lower
        upper_bounds = problem.bounds.upper

        # Handle infinite bounds
        if not np.isfinite(lower_bounds).all() or not np.isfinite(upper_bounds).all():
            lower_bounds = np.where(np.isfinite(lower_bounds), lower_bounds, -1000.0)
            upper_bounds = np.where(np.isfinite(upper_bounds), upper_bounds, 1000.0)
            warnings.warn(
                "PyGAD requires finite bounds. Infinite bounds have been replaced "
                "with [-1000, 1000]. Consider providing finite bounds for better results."
            )

        # Set random seed if provided for reproducibility
        if self.seed is not None:
            np.random.seed(self.seed)

        # Handle initial population and population size
        if self.initial_population is not None:
            initial_population = np.asarray(self.initial_population)
            population_size = len(initial_population)
            num_genes = len(initial_population[0])

            # Validate initial population dimensions
            if num_genes != len(x0):
                raise ValueError(
                    f"Initial population has {num_genes} genes but problem has "
                    f"{len(x0)} parameters. They must match."
                )
        else:
            # Calculate population size
            population_size = get_population_size(
                population_size=self.population_size, x=x0, lower_bound=10
            )
            num_genes = len(x0)

            # Create initial population that includes the starting point
            initial_population = np.random.uniform(
                lower_bounds,
                upper_bounds,
                size=(population_size, num_genes)
            )
            initial_population[0] = np.clip(x0, lower_bounds, upper_bounds)

        # Calculate number of parents for mating
        num_parents_mating = self.num_parents_mating
        if num_parents_mating is None:
            num_parents_mating = max(2, population_size // 2)

        # Ensure num_parents_mating is valid
        if num_parents_mating >= population_size:
            num_parents_mating = max(1, population_size - 1)

        # Create fitness function wrapper
        def fitness_function(_ga_instance, solution, _solution_idx):
            """Fitness function wrapper for PyGAD.

            PyGAD expects higher values to be better, but Optimagic minimizes.
            So we need to negate the objective function value.

            Args:
                _ga_instance: PyGAD GA instance (unused but required by PyGAD interface)
                solution: Current solution vector
                _solution_idx: Solution index (unused but required by PyGAD interface)
            """
            try:
                # Ensure solution is within bounds (safety check)
                solution_clipped = np.clip(solution, lower_bounds, upper_bounds)

                # Convert solution to the format expected by the problem
                fun_value = problem.fun(solution_clipped)

                # PyGAD maximizes fitness, so negate for minimization
                fitness_value = -float(fun_value)

                # Add penalty for solutions outside bounds
                if not np.allclose(solution, solution_clipped):
                    penalty = np.sum(np.abs(solution - solution_clipped)) * 1e6
                    fitness_value -= penalty

                return fitness_value
            except Exception as e:
                # Return a very bad fitness value if evaluation fails
                warnings.warn(f"Function evaluation failed: {e}")
                return -1e10

        # Set up gene space for bounds (if not provided by user)
        gene_space = self.gene_space
        if gene_space is None:
            gene_space = []
            for i in range(num_genes):
                gene_space.append({
                    'low': float(lower_bounds[i]),
                    'high': float(upper_bounds[i])
                })

        # Create PyGAD instance with all parameters
        ga_instance = pygad.GA(
            num_generations=self.stopping_maxiter,
            num_parents_mating=num_parents_mating,
            fitness_func=fitness_function,
            fitness_batch_size=self.fitness_batch_size,
            initial_population=initial_population,
            sol_per_pop=population_size,
            num_genes=num_genes,
            init_range_low=self.init_range_low,
            init_range_high=self.init_range_high,
            gene_type=self.gene_type,
            parent_selection_type=self.parent_selection_type,
            keep_parents=self.keep_parents,
            keep_elitism=self.keep_elitism,
            K_tournament=self.K_tournament,
            crossover_type=self.crossover_type,
            crossover_probability=self.crossover_probability,
            mutation_type=self.mutation_type,
            mutation_probability=self.mutation_probability,
            mutation_by_replacement=self.mutation_by_replacement,
            mutation_percent_genes=self.mutation_percent_genes,
            mutation_num_genes=self.mutation_num_genes,
            random_mutation_min_val=self.random_mutation_min_val,
            random_mutation_max_val=self.random_mutation_max_val,
            gene_space=gene_space,
            allow_duplicate_genes=self.allow_duplicate_genes,
            save_best_solutions=self.save_best_solutions,
            save_solutions=self.save_solutions,
            suppress_warnings=self.suppress_warnings,
            stop_criteria=self.stop_criteria,
            parallel_processing=self.parallel_processing,
            random_seed=self.seed,
        )

        # Run the genetic algorithm
        try:
            ga_instance.run()
        except Exception as e:
            # If the GA run fails, return a failure result
            return InternalOptimizeResult(
                x=x0,
                fun=np.inf,
                success=False,
                message=f"PyGAD optimization failed: {str(e)}",
                n_fun_evals=0,
                n_jac_evals=None,
                n_hess_evals=None,
                n_iterations=0,
                status=None,
                jac=None,
                hess=None,
                hess_inv=None,
                max_constraint_violation=None,
                info={"error": str(e)},
                history=None,
            )

        # Get the best solution
        try:
            best_solution, best_fitness, _ = ga_instance.best_solution()  # best_idx unused
        except Exception as e:
            # If getting the best solution fails, return a failure result
            return InternalOptimizeResult(
                x=x0,
                fun=np.inf,
                success=False,
                message=f"Failed to retrieve best solution: {str(e)}",
                n_fun_evals=getattr(ga_instance, 'generations_completed', 0) * population_size,
                n_jac_evals=None,
                n_hess_evals=None,
                n_iterations=getattr(ga_instance, 'generations_completed', 0),
                status=None,
                jac=None,
                hess=None,
                hess_inv=None,
                max_constraint_violation=None,
                info={"error": str(e)},
                history=None,
            )

        # Convert fitness back to objective function value (negate back)
        best_fun_value = -best_fitness

        # Determine success based on multiple criteria
        success = (
            np.isfinite(best_fun_value) and
            best_fun_value < 1e9 and
            best_fitness > -1e9 and  # Ensure we didn't get the penalty value
            ga_instance.generations_completed > 0
        )

        # Generate appropriate message
        if success:
            message = f"Optimization completed successfully after {ga_instance.generations_completed} generations"
        else:
            if not np.isfinite(best_fun_value):
                message = "Optimization failed: non-finite objective value"
            elif best_fitness <= -1e9:
                message = "Optimization failed: all evaluations resulted in errors"
            else:
                message = "Optimization completed but may not have converged"

        # Calculate actual number of function evaluations
        # This is more accurate than generations * population_size
        n_fun_evals = getattr(ga_instance, 'generations_completed', 0) * population_size

        # Get additional information from PyGAD
        best_solution_generation = getattr(ga_instance, 'best_solution_generation', None)

        # Ensure solution is within bounds (final safety check)
        best_solution_clipped = np.clip(best_solution, lower_bounds, upper_bounds)
        if not np.allclose(best_solution, best_solution_clipped):
            warnings.warn("Best solution was outside bounds and has been clipped.")
            best_solution = best_solution_clipped

        # Create result
        result = InternalOptimizeResult(
            x=best_solution,
            fun=best_fun_value,
            success=success,
            message=message,
            n_fun_evals=n_fun_evals,
            n_jac_evals=None,
            n_hess_evals=None,
            n_iterations=ga_instance.generations_completed,
            status=None,
            jac=None,
            hess=None,
            hess_inv=None,
            max_constraint_violation=None,
            info={
                "best_fitness": best_fitness,
                "best_solution_generation": best_solution_generation,
                "population_size": population_size,
                "num_parents_mating": num_parents_mating,
                "algorithm_name": "PyGAD Genetic Algorithm",
                "parent_selection_type": self.parent_selection_type,
                "crossover_type": self.crossover_type,
                "mutation_type": self.mutation_type,
            },
            history=None,
        )

        return result
