#!/usr/bin/env python3
"""
Comprehensive example demonstrating PyGAD optimizer with user-defined functions in optimagic.

This example shows how to use custom parent selection, crossover, and mutation
functions with proper type annotations using Protocol types.
"""

import numpy as np
from numpy.typing import NDArray
from typing import Any
import optimagic as om


def custom_parent_selection(
    fitness: NDArray[np.float64],
    num_parents: int,
    ga_instance: Any
) -> tuple[NDArray[np.float64], NDArray[np.int_]]:
    """
    Custom parent selection using tournament selection with tournament size 3.
    
    Args:
        fitness: Array of fitness values for all solutions in the population
        num_parents: Number of parents to select
        ga_instance: The PyGAD GA instance
        
    Returns:
        Tuple of (selected_parents, parent_indices)
    """
    population = ga_instance.population
    tournament_size = 3
    
    selected_parents = []
    parent_indices = []
    
    for _ in range(num_parents):
        # Select random individuals for tournament
        tournament_indices = np.random.choice(
            len(fitness), size=tournament_size, replace=False
        )
        
        # Find the best individual in the tournament
        tournament_fitness = fitness[tournament_indices]
        winner_idx = tournament_indices[np.argmax(tournament_fitness)]
        
        selected_parents.append(population[winner_idx].copy())
        parent_indices.append(winner_idx)
    
    return np.array(selected_parents), np.array(parent_indices, dtype=np.int_)


def custom_crossover_function(
    parents: NDArray[np.float64],
    offspring_size: tuple[int, int],
    ga_instance: Any
) -> NDArray[np.float64]:
    """
    Custom crossover function implementing blend crossover (BLX-α).
    
    Blend crossover creates offspring by selecting values from an extended range
    around the parent values, promoting exploration.
    
    Args:
        parents: 2D array of parent solutions selected for mating
        offspring_size: Tuple (num_offspring, num_genes) specifying offspring dimensions
        ga_instance: The PyGAD GA instance
        
    Returns:
        2D array of offspring solutions
    """
    num_offspring, num_genes = offspring_size
    alpha = 0.5  # Blend parameter
    offspring = []
    
    for _ in range(num_offspring):
        # Select two random parents
        parent1_idx = np.random.randint(0, parents.shape[0])
        parent2_idx = np.random.randint(0, parents.shape[0])
        
        parent1 = parents[parent1_idx]
        parent2 = parents[parent2_idx]
        
        # Blend crossover for each gene
        child = np.zeros(num_genes)
        for j in range(num_genes):
            # Calculate the range and extension
            min_val = min(parent1[j], parent2[j])
            max_val = max(parent1[j], parent2[j])
            range_val = max_val - min_val
            
            # Extended range with alpha
            lower_bound = min_val - alpha * range_val
            upper_bound = max_val + alpha * range_val
            
            # Generate offspring gene value
            child[j] = np.random.uniform(lower_bound, upper_bound)
        
        offspring.append(child)
    
    return np.array(offspring)


def custom_mutation_function(
    offspring: NDArray[np.float64],
    ga_instance: Any
) -> NDArray[np.float64]:
    """
    Custom mutation function implementing polynomial mutation.
    
    Polynomial mutation is a real-valued mutation operator that creates offspring
    by adding a small perturbation to each gene with a certain probability.
    
    Args:
        offspring: 2D array of offspring solutions to be mutated
        ga_instance: The PyGAD GA instance
        
    Returns:
        2D array of mutated offspring solutions
    """
    mutation_probability = 0.1
    eta_m = 20  # Distribution index for polynomial mutation
    
    mutated_offspring = offspring.copy()
    
    for i in range(offspring.shape[0]):
        for j in range(offspring.shape[1]):
            if np.random.random() < mutation_probability:
                # Get bounds from ga_instance if available
                if hasattr(ga_instance, 'init_range_low') and hasattr(ga_instance, 'init_range_high'):
                    lower_bound = ga_instance.init_range_low[j] if isinstance(ga_instance.init_range_low, np.ndarray) else ga_instance.init_range_low
                    upper_bound = ga_instance.init_range_high[j] if isinstance(ga_instance.init_range_high, np.ndarray) else ga_instance.init_range_high
                else:
                    # Default bounds
                    lower_bound = -5.0
                    upper_bound = 5.0
                
                # Polynomial mutation
                y = offspring[i, j]
                delta1 = (y - lower_bound) / (upper_bound - lower_bound)
                delta2 = (upper_bound - y) / (upper_bound - lower_bound)
                
                rand = np.random.random()
                mut_pow = 1.0 / (eta_m + 1.0)
                
                if rand <= 0.5:
                    xy = 1.0 - delta1
                    val = 2.0 * rand + (1.0 - 2.0 * rand) * (xy ** (eta_m + 1.0))
                    deltaq = val ** mut_pow - 1.0
                else:
                    xy = 1.0 - delta2
                    val = 2.0 * (1.0 - rand) + 2.0 * (rand - 0.5) * (xy ** (eta_m + 1.0))
                    deltaq = 1.0 - val ** mut_pow
                
                # Apply mutation
                mutated_offspring[i, j] = y + deltaq * (upper_bound - lower_bound)
                
                # Ensure bounds
                mutated_offspring[i, j] = np.clip(mutated_offspring[i, j], lower_bound, upper_bound)
    
    return mutated_offspring


def main():
    """Main function demonstrating the usage of PyGAD with custom functions."""
    
    # Define the objective function (Rastrigin function to minimize)
    def rastrigin(x):
        A = 10
        n = len(x)
        return A * n + np.sum(x**2 - A * np.cos(2 * np.pi * x))
    
    # Initial parameters
    initial_params = np.array([2.0, 3.0, -1.0, 0.5])
    
    # Define bounds
    bounds = om.Bounds(
        lower=[-5.12] * len(initial_params),
        upper=[5.12] * len(initial_params)
    )
    
    print("Optimizing Rastrigin function using PyGAD with custom operators...")
    print(f"Initial parameters: {initial_params}")
    print(f"Bounds: {bounds.lower} to {bounds.upper}")
    
    # Run optimization with custom functions
    result = om.minimize(
        fun=rastrigin,
        params=initial_params,
        algorithm="pygad",
        bounds=bounds,
        algo_options={
            "num_generations": 100,
            "population_size": 50,
            "num_parents_mating": 25,
            "parent_selection_type": custom_parent_selection,
            "crossover_type": custom_crossover_function,
            "mutation_type": custom_mutation_function,
            "fitness_batch_size": 10,  # Enable parallel processing
            "suppress_warnings": False,  # Show warnings to see custom function messages
        }
    )
    
    print(f"\nOptimization completed!")
    print(f"Best parameters: {result.params}")
    print(f"Best function value: {result.fun}")
    print(f"Success: {result.success}")
    print(f"Message: {result.message}")


if __name__ == "__main__":
    main()
