#!/usr/bin/env python3
"""Test script for PyGAD optimizer implementation."""

import sys
import numpy as np
from dataclasses import dataclass
from typing import Any

# Add optimagic to path
sys.path.insert(0, 'optimagic/src')

# Mock the required optimagic components for testing
class MockBounds:
    def __init__(self, lower, upper):
        self.lower = np.asarray(lower)
        self.upper = np.asarray(upper)

class MockInternalOptimizationProblem:
    def __init__(self, fun, bounds):
        self.fun = fun
        self.bounds = bounds

@dataclass
class MockInternalOptimizeResult:
    x: np.ndarray
    fun: float
    success: bool
    message: str
    n_fun_evals: int = 0
    n_jac_evals: int = None
    n_hess_evals: int = None
    n_iterations: int = 0
    status: Any = None
    jac: Any = None
    hess: Any = None
    hess_inv: Any = None
    max_constraint_violation: Any = None
    info: dict = None
    history: Any = None

def test_pygad_optimizer():
    """Test the PyGAD optimizer implementation."""
    print("Testing PyGAD optimizer implementation...")
    
    try:
        # Import the optimizer
        from optimagic.optimizers.pygad_optimizers import PyGADGA
        print("✓ Successfully imported PyGADGA")
        
        # Create optimizer instance
        optimizer = PyGADGA(
            population_size=20,
            stopping_maxiter=50,
            mutation_type="random",
            seed=42
        )
        print("✓ Successfully created optimizer instance")
        
        # Define test problem (sphere function)
        def sphere_function(x):
            return np.sum(x**2)
        
        # Create problem
        bounds = MockBounds(lower=[-5.0, -5.0], upper=[5.0, 5.0])
        problem = MockInternalOptimizationProblem(sphere_function, bounds)
        x0 = np.array([2.0, 3.0])
        
        print("✓ Test problem setup complete")
        
        # Test the optimizer
        result = optimizer._solve_internal_problem(problem, x0)
        
        print("✓ Optimization completed successfully")
        print(f"  Final solution: {result.x}")
        print(f"  Final objective: {result.fun}")
        print(f"  Success: {result.success}")
        print(f"  Message: {result.message}")
        print(f"  Iterations: {result.n_iterations}")
        print(f"  Function evaluations: {result.n_fun_evals}")
        
        # Verify result quality
        if result.success and result.fun < 1.0:  # Should be close to 0 for sphere function
            print("✓ Optimization result looks good")
        else:
            print("⚠ Optimization result may not be optimal")
            
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pygad_optimizer()
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
