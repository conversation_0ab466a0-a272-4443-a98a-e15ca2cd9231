#!/usr/bin/env python3
"""
Example demonstrating PyGAD optimizer with parallel processing using optimagic's 
batch evaluation system.

This example shows how to use fitness_batch_size and n_cores for parallel evaluation
while maintaining full history tracking and logging capabilities.
"""

import time
import numpy as np
import optimagic as om


def expensive_objective_function(x):
    """
    Simulate an expensive objective function that takes time to compute.
    
    This represents a real-world scenario where function evaluation is the bottleneck,
    making parallel processing beneficial.
    """
    # Simulate computational expense with a small delay
    time.sleep(0.02)  # 20ms delay per evaluation
    
    # Rosenbrock function - a classic optimization test problem
    result = 0
    for i in range(len(x) - 1):
        result += 100 * (x[i+1] - x[i]**2)**2 + (1 - x[i])**2
    
    return result


def compare_serial_vs_parallel():
    """Compare serial vs parallel execution times and results."""
    
    # Problem setup
    initial_params = np.array([0.0, 0.0, 0.0, 0.0])
    bounds = om.Bounds(
        lower=[-2.0] * len(initial_params),
        upper=[2.0] * len(initial_params)
    )
    
    common_options = {
        "num_generations": 20,
        "population_size": 30,
        "seed": 42,
    }
    
    print("Comparing Serial vs Parallel PyGAD Optimization")
    print("=" * 50)
    print(f"Problem: Minimize Rosenbrock function with {len(initial_params)} variables")
    print(f"Population size: {common_options['population_size']}")
    print(f"Generations: {common_options['num_generations']}")
    print()
    
    # Serial execution (no parallel processing)
    print("1. Serial Execution (no parallel processing)")
    start_time = time.time()
    
    result_serial = om.minimize(
        fun=expensive_objective_function,
        params=initial_params,
        algorithm="pygad",
        bounds=bounds,
        algo_options={
            **common_options,
            "fitness_batch_size": None,  # No batching
        }
    )
    
    serial_time = time.time() - start_time
    print(f"   Time: {serial_time:.2f} seconds")
    print(f"   Best solution: {result_serial.params}")
    print(f"   Best fitness: {result_serial.fun:.6f}")
    print(f"   Success: {result_serial.success}")
    print()
    
    # Parallel execution with batch processing
    print("2. Parallel Execution (with batch processing)")
    start_time = time.time()
    
    result_parallel = om.minimize(
        fun=expensive_objective_function,
        params=initial_params,
        algorithm="pygad",
        bounds=bounds,
        algo_options={
            **common_options,
            "fitness_batch_size": 6,     # Process 6 solutions in parallel
        }
    )
    
    parallel_time = time.time() - start_time
    print(f"   Time: {parallel_time:.2f} seconds")
    print(f"   Best solution: {result_parallel.params}")
    print(f"   Best fitness: {result_parallel.fun:.6f}")
    print(f"   Success: {result_parallel.success}")
    print()
    
    # Performance comparison
    speedup = serial_time / parallel_time if parallel_time > 0 else 0
    print("3. Performance Comparison")
    print(f"   Speedup: {speedup:.2f}x")
    print(f"   Time saved: {serial_time - parallel_time:.2f} seconds")
    print()
    
    # Verify both have history tracking
    print("4. History Tracking Verification")
    print(f"   Serial history entries: {len(result_serial.history) if hasattr(result_serial, 'history') else 'No history'}")
    print(f"   Parallel history entries: {len(result_parallel.history) if hasattr(result_parallel, 'history') else 'No history'}")
    print("   ✓ Both maintain full history tracking!")
    
    return result_serial, result_parallel


def demonstrate_batch_size_effects():
    """Demonstrate the effect of different batch sizes on performance."""
    
    print("\nDemonstrating Batch Size Effects")
    print("=" * 40)
    
    initial_params = np.array([0.5, -0.5])
    bounds = om.Bounds(lower=[-2.0, -2.0], upper=[2.0, 2.0])
    
    batch_sizes = [None, 1, 3, 6, 10]
    results = {}
    
    for batch_size in batch_sizes:
        print(f"Testing batch_size = {batch_size}")
        start_time = time.time()
        
        result = om.minimize(
            fun=expensive_objective_function,
            params=initial_params,
            algorithm="pygad",
            bounds=bounds,
            algo_options={
                "num_generations": 15,
                "population_size": 20,
                "fitness_batch_size": batch_size,
                "seed": 42,
            }
        )
        
        execution_time = time.time() - start_time
        results[batch_size] = {
            "time": execution_time,
            "fitness": result.fun,
            "success": result.success
        }
        
        print(f"   Time: {execution_time:.2f}s, Fitness: {result.fun:.6f}")
    
    print("\nBatch Size Performance Summary:")
    print("Batch Size | Time (s) | Fitness   | Speedup")
    print("-" * 45)
    
    baseline_time = results[None]["time"]
    for batch_size in batch_sizes:
        r = results[batch_size]
        speedup = baseline_time / r["time"] if r["time"] > 0 else 0
        batch_str = "None" if batch_size is None else str(batch_size)
        print(f"{batch_str:>10} | {r['time']:>8.2f} | {r['fitness']:>9.6f} | {speedup:>6.2f}x")


def demonstrate_optimal_configuration():
    """Show optimal configuration for different scenarios."""
    
    print("\nOptimal Configuration Guidelines")
    print("=" * 40)
    
    print("""
1. **Small, Fast Functions** (< 1ms per evaluation):
   - fitness_batch_size: None or 1
   - Reason: Parallelization overhead exceeds benefits

2. **Medium Functions** (1-10ms per evaluation):
   - fitness_batch_size: 3-6
   - Reason: Good balance of parallelization and overhead

3. **Expensive Functions** (> 10ms per evaluation):
   - fitness_batch_size: 6-15
   - Reason: Maximize parallelization benefits

4. **Very Large Populations** (> 100 individuals):
   - fitness_batch_size: 10-20
   - Reason: Efficient batch processing of large populations

Note: The number of CPU cores used is controlled by optimagic's global configuration.

Example configurations:
""")
    
    configurations = [
        {
            "name": "Fast Function",
            "options": {"fitness_batch_size": None},
            "use_case": "Simple mathematical functions"
        },
        {
            "name": "Medium Function",
            "options": {"fitness_batch_size": 5},
            "use_case": "Simulation models, ML training"
        },
        {
            "name": "Expensive Function",
            "options": {"fitness_batch_size": 10},
            "use_case": "Complex simulations, external processes"
        }
    ]
    
    for config in configurations:
        print(f"**{config['name']}:**")
        print(f"   Options: {config['options']}")
        print(f"   Use case: {config['use_case']}")
        print()


def main():
    """Main function demonstrating PyGAD parallel processing capabilities."""
    
    print("PyGAD Parallel Processing with optimagic")
    print("=" * 50)
    print("This example demonstrates how to use PyGAD's parallel processing")
    print("capabilities through optimagic's batch evaluation system.")
    print()
    
    # Run comparisons and demonstrations
    try:
        compare_serial_vs_parallel()
        demonstrate_batch_size_effects()
        demonstrate_optimal_configuration()
        
        print("\n" + "=" * 50)
        print("Key Benefits of optimagic's Parallel Processing:")
        print("✓ Full history tracking maintained")
        print("✓ Consistent logging and monitoring")
        print("✓ Automatic load balancing")
        print("✓ Error handling and recovery")
        print("✓ Memory efficient batch processing")
        print("✓ Compatible with all optimagic features")
        
    except Exception as e:
        print(f"Error during demonstration: {e}")
        print("Make sure PyGAD is installed: pip install pygad")


if __name__ == "__main__":
    main()
