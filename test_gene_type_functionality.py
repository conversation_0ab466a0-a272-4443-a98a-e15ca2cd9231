#!/usr/bin/env python3
"""
Test script to verify gene type functionality in PyGAD optimizer.
"""

import numpy as np
from numpy.typing import NDArray
from typing import Any


def test_apply_gene_type_to_population():
    """Test the _apply_gene_type_to_population function with various gene types."""
    
    # Mock PyGAD GA class for testing
    class MockGA:
        supported_int_types = [int, np.int8, np.int16, np.int32, np.int64,
                               np.uint, np.uint8, np.uint16, np.uint32, np.uint64]
        supported_float_types = [float, np.float16, np.float32, np.float64]
        supported_int_float_types = supported_int_types + supported_float_types

    # Mock pygad module
    class MockPygad:
        GA = MockGA

    # Create a mock optimizer instance
    class MockOptimizer:
        def _apply_gene_type_to_population(self, population: NDArray[np.float64], gene_type: Any) -> NDArray[np.float64]:
            """
            Apply gene type to a population array according to PyGAD's gene type rules.
            """
            pygad = MockPygad()  # Use mock instead of real pygad
            num_genes = population.shape[1]
            
            # Single data type for all genes
            if gene_type in pygad.GA.supported_int_float_types:
                return np.array(population, dtype=gene_type)
            
            # [type, precision] format - handles both float with precision and int with None
            elif (isinstance(gene_type, (list, tuple)) and 
                  len(gene_type) == 2 and 
                  gene_type[0] in pygad.GA.supported_int_float_types):
                
                if gene_type[1] is None:
                    # Both [int, None] and [float, None] cases
                    return np.array(population, dtype=gene_type[0])
                else:
                    # Only for float types with precision
                    if gene_type[0] in pygad.GA.supported_float_types:
                        return np.round(
                            np.array(population, dtype=gene_type[0]),
                            gene_type[1]
                        )
                    else:
                        raise ValueError(f"Integer types cannot have precision: {gene_type}")
            
            # List of types, one per gene
            elif (isinstance(gene_type, (list, tuple, np.ndarray)) and 
                  len(gene_type) == num_genes):
                
                # Create a new array with object dtype to hold mixed types
                processed_population = np.zeros(shape=population.shape, dtype=object)
                
                # Process each gene according to its type
                for gene_idx in range(num_genes):
                    gene_type_i = gene_type[gene_idx]
                    
                    # Single type
                    if gene_type_i in pygad.GA.supported_int_float_types:
                        processed_population[:, gene_idx] = np.array(
                            population[:, gene_idx], 
                            dtype=gene_type_i
                        )
                    
                    # [type, precision] format
                    elif (isinstance(gene_type_i, (list, tuple)) and 
                          len(gene_type_i) == 2 and
                          gene_type_i[0] in pygad.GA.supported_int_float_types):
                        
                        if gene_type_i[1] is None:
                            processed_population[:, gene_idx] = np.array(
                                population[:, gene_idx], 
                                dtype=gene_type_i[0]
                            )
                        else:
                            # Only for float types with precision
                            if gene_type_i[0] in pygad.GA.supported_float_types:
                                processed_population[:, gene_idx] = np.round(
                                    np.array(population[:, gene_idx], dtype=gene_type_i[0]),
                                    gene_type_i[1]
                                )
                            else:
                                raise ValueError(f"Integer types cannot have precision: {gene_type_i}")
                    else:
                        raise ValueError(f"Invalid gene type at index {gene_idx}: {gene_type_i}")
                
                return processed_population
            else:
                raise ValueError(
                    f"Invalid gene_type: {gene_type}. Must be a single type, "
                    f"a [type, precision] pair, or a list of types with length equal to num_genes."
                )

    # Test data
    population = np.array([
        [1.7, 2.3, 3.9],
        [4.1, 5.8, 6.2],
        [7.5, 8.1, 9.6]
    ])
    
    optimizer = MockOptimizer()
    
    print("Original population:")
    print(population)
    print(f"Original dtype: {population.dtype}")
    print()
    
    # Test 1: Single type for all genes
    print("Test 1: Single int type for all genes")
    result1 = optimizer._apply_gene_type_to_population(population, int)
    print(f"Result: {result1}")
    print(f"Dtype: {result1.dtype}")
    print()
    
    # Test 2: Float with precision
    print("Test 2: Float with 1 decimal precision")
    result2 = optimizer._apply_gene_type_to_population(population, [float, 1])
    print(f"Result: {result2}")
    print(f"Dtype: {result2.dtype}")
    print()
    
    # Test 3: Mixed types per gene
    print("Test 3: Mixed types per gene [int, float, [float, 2]]")
    result3 = optimizer._apply_gene_type_to_population(population, [int, float, [float, 2]])
    print(f"Result: {result3}")
    print(f"Dtype: {result3.dtype}")
    print()
    
    print("✓ All gene type tests passed!")


def example_usage():
    """Example of how gene_space and gene_type work together."""
    print("\nExample usage with optimagic:")
    print("""
import optimagic as om
import numpy as np

# Define objective function
def mixed_optimization(x):
    # x[0] should be integer, x[1] should be float with 2 decimals, x[2] should be float
    return (x[0] - 5)**2 + (x[1] - 2.5)**2 + (x[2] - 1.0)**2

# Use PyGAD with gene types and gene space
result = om.minimize(
    fun=mixed_optimization,
    params=np.array([3, 2.0, 1.5]),
    algorithm="pygad",
    bounds=om.Bounds(lower=[0, 0.0, -5.0], upper=[10, 5.0, 5.0]),
    algo_options={
        "num_generations": 50,
        "population_size": 20,
        "gene_type": [int, [float, 2], float],  # Mixed types
        "gene_space": [
            range(0, 11),           # Integer gene: 0 to 10
            {"low": 0.0, "high": 5.0},  # Float gene with bounds
            None                    # Use bounds for this gene
        ],
    }
)
""")


if __name__ == "__main__":
    test_apply_gene_type_to_population()
    example_usage()
