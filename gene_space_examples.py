#!/usr/bin/env python3
"""
Comprehensive examples of PyGAD gene_space parameter formats.

This file demonstrates all the different ways to specify gene_space in PyGAD,
which is used in the Optimagic PyGAD optimizer implementation.
"""

import numpy as np

def demonstrate_gene_space_formats():
    """Demonstrate all supported gene_space formats in PyGAD."""
    
    print("=" * 80)
    print("PyGAD gene_space Parameter Format Examples")
    print("=" * 80)
    
    # 1. Same discrete space for all genes
    print("\n1. SAME DISCRETE SPACE FOR ALL GENES")
    print("-" * 40)
    
    # All genes can only have these specific values
    gene_space_1a = [0.3, 5.2, -4, 8]
    print(f"gene_space = {gene_space_1a}")
    print("   → All genes can only be: 0.3, 5.2, -4, or 8")
    
    # Using range for all genes
    gene_space_1b = range(10)
    print(f"gene_space = range(10)")
    print("   → All genes can be: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9")
    
    # Using numpy array
    gene_space_1c = np.arange(0, 10, 0.5)
    print(f"gene_space = np.arange(0, 10, 0.5)")
    print("   → All genes can be: 0.0, 0.5, 1.0, 1.5, ..., 9.5")
    
    # 2. Different discrete space per gene
    print("\n2. DIFFERENT DISCRETE SPACE PER GENE")
    print("-" * 40)
    
    gene_space_2 = [
        [0.4, -5],              # Gene 0: can be 0.4 or -5
        [0.5, -3.2, 8.2, -9],   # Gene 1: can be 0.5, -3.2, 8.2, or -9
        [1, 2, 3]               # Gene 2: can be 1, 2, or 3
    ]
    print(f"gene_space = {gene_space_2}")
    print("   → Gene 0: can be 0.4 or -5")
    print("   → Gene 1: can be 0.5, -3.2, 8.2, or -9")
    print("   → Gene 2: can be 1, 2, or 3")
    
    # 3. Continuous ranges using dictionaries
    print("\n3. CONTINUOUS RANGES")
    print("-" * 40)
    
    # Same continuous range for all genes
    gene_space_3a = {"low": 1, "high": 5}
    print(f"gene_space = {gene_space_3a}")
    print("   → All genes: continuous range [1, 5) - 1 included, 5 excluded")
    
    # Different continuous ranges per gene
    gene_space_3b = [
        {"low": 0, "high": 10},    # Gene 0: [0, 10)
        {"low": -5, "high": 5}     # Gene 1: [-5, 5)
    ]
    print(f"gene_space = {gene_space_3b}")
    print("   → Gene 0: continuous range [0, 10)")
    print("   → Gene 1: continuous range [-5, 5)")
    
    # 4. Discrete ranges with step
    print("\n4. DISCRETE RANGES WITH STEP")
    print("-" * 40)
    
    gene_space_4a = {"low": 0, "high": 10, "step": 2}
    print(f"gene_space = {gene_space_4a}")
    print("   → All genes can be: 0, 2, 4, 6, 8 (step=2)")
    
    gene_space_4b = [
        {"low": 1, "high": 5, "step": 0.5},   # Gene 0: 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5
        {"low": 10, "high": 20, "step": 3}    # Gene 1: 10, 13, 16, 19
    ]
    print(f"gene_space = {gene_space_4b}")
    print("   → Gene 0: 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5")
    print("   → Gene 1: 10, 13, 16, 19")
    
    # 5. Mixed formats
    print("\n5. MIXED FORMATS")
    print("-" * 40)
    
    gene_space_5 = [
        [1, 2, 3],                    # Gene 0: discrete values
        {"low": 0, "high": 1},        # Gene 1: continuous range
        range(5, 10),                 # Gene 2: discrete range 5-9
        None                          # Gene 3: use init_range_* parameters
    ]
    print(f"gene_space = {gene_space_5}")
    print("   → Gene 0: discrete values [1, 2, 3]")
    print("   → Gene 1: continuous range [0, 1)")
    print("   → Gene 2: discrete range [5, 6, 7, 8, 9]")
    print("   → Gene 3: uses init_range_low/high parameters")
    
    # 6. Fixed gene values
    print("\n6. FIXED GENE VALUES")
    print("-" * 40)
    
    gene_space_6 = [
        5,                            # Gene 0: always 5 (fixed)
        [1, 2, 3],                    # Gene 1: can be 1, 2, or 3
        {"low": 0, "high": 1}         # Gene 2: continuous [0, 1)
    ]
    print(f"gene_space = {gene_space_6}")
    print("   → Gene 0: always 5 (fixed value)")
    print("   → Gene 1: can be 1, 2, or 3")
    print("   → Gene 2: continuous range [0, 1)")
    
    # 7. Real-world examples
    print("\n7. REAL-WORLD EXAMPLES")
    print("-" * 40)
    
    # Example: Hyperparameter optimization
    hyperparams_space = [
        [0.001, 0.01, 0.1, 1.0],                    # Learning rate
        [16, 32, 64, 128],                          # Batch size
        {"low": 0.0, "high": 0.5},                  # Dropout rate (continuous)
        [1, 2, 3, 4, 5],                            # Number of layers
        {"low": 10, "high": 1000, "step": 10}       # Hidden units (10, 20, ..., 990)
    ]
    print("Hyperparameter optimization example:")
    print("   → Learning rate: [0.001, 0.01, 0.1, 1.0]")
    print("   → Batch size: [16, 32, 64, 128]")
    print("   → Dropout rate: continuous [0.0, 0.5)")
    print("   → Number of layers: [1, 2, 3, 4, 5]")
    print("   → Hidden units: [10, 20, 30, ..., 990]")
    
    # Example: Portfolio optimization
    portfolio_space = [
        {"low": 0.0, "high": 1.0},  # Stock 1 weight
        {"low": 0.0, "high": 1.0},  # Stock 2 weight
        {"low": 0.0, "high": 1.0},  # Stock 3 weight
        # Note: You'd need additional constraints to ensure weights sum to 1
    ]
    print("\nPortfolio optimization example:")
    print("   → Each stock weight: continuous [0.0, 1.0)")
    print("   → Additional constraint needed: sum of weights = 1")
    
    print("\n" + "=" * 80)
    print("IMPORTANT NOTES:")
    print("=" * 80)
    print("• Continuous ranges [low, high) include 'low' but exclude 'high'")
    print("• When using step, the range becomes discrete")
    print("• None means use init_range_low/high for that gene")
    print("• Fixed values (single numbers) keep the gene constant")
    print("• Order matters: gene_space[i] defines space for gene i")
    print("• PyGAD handles mutation differently for discrete vs continuous spaces")

if __name__ == "__main__":
    demonstrate_gene_space_formats()
